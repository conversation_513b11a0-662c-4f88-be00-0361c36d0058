/**
 * Pegasus Wing Fortune Game
 * A simple fortune-telling game with 5 wings
 */

// Game Configuration
const CONFIG = {
  WINGS_COUNT: 5,
  RESULTS: {
    DAIKICHI: { name: '大吉', weight: 0.20, message: '大吉！最高の一日になりそう', color: 'success' },
    KICHI: { name: '吉', weight: 0.20, message: '吉！良い流れに乗れそう', color: 'primary' },
    SHOKICHI: { name: '小吉', weight: 0.20, message: '小吉。肩の力を抜いて◎', color: 'neutral' },
    TONKOTSU: { name: '豚骨', weight: 0.20, message: '豚骨！替え玉いっとく？', color: 'neutral' },
    KAEDAMA: { name: '替え玉', weight: 0.20, message: '替え玉！もう一杯いきますか', color: 'primary' }
  },
  REVEAL_DELAY: 1000,
  ANIMATION_DELAY: 500
};

// Game State
let gameState = {
  phase: 'IDLE', // IDLE, PICKED, REVE<PERSON>_ALL, COMPLETE
  wings: [],
  selectedWingIndex: -1,
  stats: {
    playCount: 0,
    bestResult: null
  }
};

// Utility Functions
const $ = (selector) => document.querySelector(selector);
const $$ = (selector) => document.querySelectorAll(selector);

// Random Number Generation (using crypto API when available)
function getSecureRandom() {
  if (window.crypto && window.crypto.getRandomValues) {
    const array = new Uint32Array(1);
    window.crypto.getRandomValues(array);
    return array[0] / (0xffffffff + 1);
  }
  return Math.random();
}

// Weighted Random Selection
function choiceWeighted(items) {
  const weights = Object.values(items).map(item => item.weight);
  const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);
  let random = getSecureRandom() * totalWeight;
  
  const keys = Object.keys(items);
  for (let i = 0; i < keys.length; i++) {
    random -= items[keys[i]].weight;
    if (random <= 0) {
      return keys[i];
    }
  }
  
  return keys[keys.length - 1]; // fallback
}

// Shuffle Array
function shuffle(array) {
  const shuffled = [...array];
  for (let i = shuffled.length - 1; i > 0; i--) {
    const j = Math.floor(getSecureRandom() * (i + 1));
    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
  }
  return shuffled;
}

// Local Storage Management
function loadStats() {
  try {
    const saved = localStorage.getItem('pegasus-wing-stats');
    if (saved) {
      gameState.stats = { ...gameState.stats, ...JSON.parse(saved) };
    }
  } catch (error) {
    console.warn('Failed to load stats:', error);
  }
}

function saveStats() {
  try {
    localStorage.setItem('pegasus-wing-stats', JSON.stringify(gameState.stats));
  } catch (error) {
    console.warn('Failed to save stats:', error);
  }
}

// Game Logic Functions
function generateResultPool() {
  const pool = [];
  
  // Ensure all 5 different results appear exactly once
  const resultKeys = Object.keys(CONFIG.RESULTS);
  
  for (let i = 0; i < CONFIG.WINGS_COUNT; i++) {
    const resultKey = resultKeys[i];
    pool.push({
      key: resultKey,
      ...CONFIG.RESULTS[resultKey]
    });
  }
  
  return shuffle(pool);
}

function initializeGame() {
  gameState.phase = 'IDLE';
  gameState.wings = generateResultPool();
  gameState.selectedWingIndex = -1;
  
  renderBoard();
  updateUI();
}

function renderBoard() {
  const board = $('#gameBoard');
  board.innerHTML = '';
  
  for (let i = 0; i < CONFIG.WINGS_COUNT; i++) {
    const wingButton = document.createElement('button');
    wingButton.className = 'wing';
    wingButton.setAttribute('aria-label', `羽 ${i + 1}`);
    wingButton.setAttribute('aria-pressed', 'false');
    wingButton.setAttribute('data-wing-index', i);
    
    const result = gameState.wings[i];
    
    wingButton.innerHTML = `
      <div class="wing__inner">
        <div class="card-front">
          <svg class="wing-icon" viewBox="0 0 300 374.56" xmlns="http://www.w3.org/2000/svg">
            <defs>
              <radialGradient id="cardPegasusGradient" cx="150" cy="187.28" r="169.67" gradientUnits="userSpaceOnUse">
                <stop offset="0" stop-color="#2ea7e0"/>
                <stop offset=".5" stop-color="#00a8b5"/>
                <stop offset="1" stop-color="#2ea7e0"/>
              </radialGradient>
              <filter id="cardCosmicGlow">
                <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                <feMerge>
                  <feMergeNode in="coloredBlur"/>
                  <feMergeNode in="SourceGraphic"/>
                </feMerge>
              </filter>
            </defs>
            <path fill="url(#cardPegasusGradient)" 
                  filter="url(#cardCosmicGlow)"
                  d="M49.29,72.62l25,25.49c10.91,2.11,22.57.03,33.69,1.13l40.3,41.28-17.41-.25,12.32,29.02c-4.29,10.34-9.07,20.52-13.41,30.83-.44,1.05-1.39,2.72-1.16,3.82,3.82-4.9,9.43-9.41,12.9-14.46,2.93-4.27,16.19-28.3,16.61-32.18.13-1.23-.44-2.17-.43-3.33.1-25.5-1.38-50.93-1.23-76.38,38.94-24.94,79.23-47.79,118-73.02l8.61-4.57-1.24,43.78-63.43,48.5,64.16-26.61c-1.55,10.59-1.6,21.42-3.04,32.02-.25,1.85-.3,4.77-1.42,6.04l-56.72,33.81,55.21-11.19-14.49,34.01c-1.12,1.42-7.48,3.08-9.63,3.8-13.59,4.57-27.4,8.54-41.04,12.93l43.27,2.73-18.93,22.36c-20.25,4.72-40.51,9.32-60.66,14.46-.56.14-1.61-.03-1.49.73,19.77.57,39.55.83,59.34,2.1,6.75,4.81,11.92,11.64,18.11,17.2l29.14,1.59,19.75,33.09v61.93l-37.05-41.79-2.22-35-9.02-1.59-4.83,19.55-12.89,17.8c4.63,6.91,10.89,13.33,15.48,20.19.73,1.09,1.84,2.21,1.69,3.56l-21.07,45.62-1.22,12.95h-36.31l20.61-23.42,9.14-29-40.69-37.37v-25.12l-10.04,16.57c-1.09,1.69-.21,5.12-2.01,5.95-15.39,2.77-30.89,5.04-46.25,7.97-2.96.56-16.51,2.72-17.82,4.06l-37.06,64.91c-2.1,4.87-2.72,10.26-3.71,15.44H29.89l26.24-31.22,17.04-59.81c-2.17,1.57-4.88,7.05-7.66,6.94-13.5-4.41-26.77-9.58-40.27-13.99-.49-.16-.47-.73-1.08.07l-2.65,22.33,16.35,6.04-7.21,31.83-28.14-28.19,3.1-55.54.71-.76,41,1.93c-1.55-6.97-2.42-14.29-2.24-21.39l18.72-43.95c1.75-2.17,8.72-2.73,11.36-4.56,2.08-1.45,3.77-5.64,6.23-7.2l1.74-14.92-13.95,15.64c-4.88,1.8-13.31,1.64-17.75,3.64-1.04.47-11.36,8.25-12.53,9.36-2.02,1.91-8.48,12.82-9.33,13.09l-23.32-6.39c-3.01-5.12-4.46-11.06-6.24-16.69,6.41-12.81,13.89-25.05,20.52-37.72,4.77-9.11,5.45-12.16,12.6-20.23,5.19-5.86,10.93-11.33,16.17-17.15v-33.08Z"/>
          </svg>
        </div>
        <div class="card-back result--${result.key.toLowerCase()}">
          <div class="result-icon">
            ${getResultIcon(result.key)}
          </div>
          <div class="result-text">${result.name}</div>
          <div class="result-message">${result.message}</div>
        </div>
      </div>
    `;
    
    board.appendChild(wingButton);
  }
}

function getResultIcon(resultKey) {
  const starSvg = `
    <svg class="result-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <filter id="iconGlow">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
      <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z" 
            fill="currentColor" filter="url(#iconGlow)"/>
    </svg>
  `;
  
  const ramenSvg = `
    <svg class="result-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
      <defs>
        <filter id="ramenGlow">
          <feGaussianBlur stdDeviation="2" result="coloredBlur"/>
          <feMerge>
            <feMergeNode in="coloredBlur"/>
            <feMergeNode in="SourceGraphic"/>
          </feMerge>
        </filter>
      </defs>
      <ellipse cx="12" cy="18" rx="8" ry="4" fill="none" stroke="currentColor" stroke-width="2" filter="url(#ramenGlow)"/>
      <path d="M4 14 Q12 12, 20 14" fill="none" stroke="currentColor" stroke-width="2"/>
      <path d="M6 14 L6 8 M10 14 L10 6 M14 14 L14 6 M18 14 L18 8" stroke="currentColor" stroke-width="1"/>
      <path d="M8 8 L8 4 M12 6 L12 2 M16 6 L16 2" stroke="currentColor" stroke-width="1"/>
    </svg>
  `;
  
  switch (resultKey) {
    case 'DAIKICHI':
    case 'KICHI':
    case 'SHOKICHI':
      return starSvg;
    case 'TONKOTSU':
    case 'KAEDAMA':
      return ramenSvg;
    default:
      return starSvg;
  }
}

function revealWing(wingIndex) {
  if (gameState.phase !== 'IDLE') {
    return;
  }
  
  const wingButton = $(`[data-wing-index="${wingIndex}"]`);
  
  if (wingButton && wingButton.classList.contains('wing--revealed')) {
    return;
  }
  
  gameState.phase = 'PICKED';
  gameState.selectedWingIndex = wingIndex;
  
  const result = gameState.wings[wingIndex];
  
  // Log card vertical position
  const rect = wingButton.getBoundingClientRect();
  console.log(`Card ${wingIndex} vertical position: ${rect.top}px`);
  
  if (!wingButton || !result) {
    return;
  }
  
  // Disable all wings
  $$('.wing').forEach(wing => {
    wing.disabled = true;
  });
  
  // Add revealed class and cosmic effects
  wingButton.classList.add('wing--revealed');
  wingButton.setAttribute('aria-pressed', 'true');
  
  enhanceWingReveal(wingButton);
  
  // Log position after reveal
  setTimeout(() => {
    const newRect = wingButton.getBoundingClientRect();
    const computedStyle = window.getComputedStyle(wingButton);
    console.log(`Card ${wingIndex} vertical position after reveal: ${newRect.top}px`);
    console.log(`Card ${wingIndex} transform: ${computedStyle.transform}`);
    console.log(`Card ${wingIndex} margin-top: ${computedStyle.marginTop}`);
    console.log(`Card ${wingIndex} classes: ${wingButton.classList.toString()}`);
    
    // Check all other cards for comparison
    $$('.wing').forEach((otherWing, index) => {
      const otherRect = otherWing.getBoundingClientRect();
      const otherStyle = window.getComputedStyle(otherWing);
      console.log(`  Card ${index} position: ${otherRect.top}px, transform: ${otherStyle.transform}, classes: ${otherWing.classList.toString()}`);
    });
  }, 100);
  
  // Show toast
  setTimeout(() => {
    showToast(result);
    
    // Update stats
    gameState.stats.playCount++;
    updateBestResult(result.key);
    saveStats();
    updateUI();
    
    // Enable play again button
    const playAgainBtn = $('#playAgain');
    if (playAgainBtn) {
      playAgainBtn.disabled = false;
    }
    
    // Auto reveal remaining wings after delay
    setTimeout(() => {
      revealAllWings();
    }, CONFIG.REVEAL_DELAY);
    
  }, CONFIG.ANIMATION_DELAY);
}

function revealAllWings() {
  if (gameState.phase !== 'PICKED') return;
  
  gameState.phase = 'REVEAL_ALL';
  
  $$('.wing').forEach((wing, index) => {
    if (index !== gameState.selectedWingIndex) {
      setTimeout(() => {
        wing.classList.add('wing--revealed');
      }, index * 100); // Stagger the reveals
    }
  });
  
  setTimeout(() => {
    gameState.phase = 'COMPLETE';
  }, CONFIG.WINGS_COUNT * 100);
}

function updateBestResult(newResult) {
  const resultRanking = ['DAIKICHI', 'KICHI', 'SHOKICHI', 'KAEDAMA', 'TONKOTSU'];
  const currentBest = gameState.stats.bestResult;
  
  if (!currentBest || resultRanking.indexOf(newResult) < resultRanking.indexOf(currentBest)) {
    gameState.stats.bestResult = newResult;
  }
}

function showToast(result) {
  const toast = $('#resultToast');
  const icon = $('#toastIcon');
  const message = $('#toastMessage');
  
  // Set content
  icon.innerHTML = getResultIcon(result.key);
  message.textContent = result.message;
  
  // Set color class
  toast.className = `toast toast--visible toast--${result.color}`;
  toast.setAttribute('aria-hidden', 'false');
  
  // Auto hide after 3 seconds
  setTimeout(() => {
    hideToast();
  }, 3000);
}

function hideToast() {
  const toast = $('#resultToast');
  toast.classList.remove('toast--visible');
  toast.setAttribute('aria-hidden', 'true');
}

function updateUI() {
  // Update stats display
  $('#playCount').textContent = gameState.stats.playCount;
  $('#bestResult').textContent = gameState.stats.bestResult 
    ? CONFIG.RESULTS[gameState.stats.bestResult].name 
    : '-';
  
  // Update button states
  $('#playAgain').disabled = gameState.phase === 'IDLE';
}

function resetGame() {
  hideToast();
  
  // Force clear all wing states
  $$('.wing').forEach((wing) => {
    wing.className = 'wing'; // Force reset all classes
    wing.disabled = false;
    wing.setAttribute('aria-pressed', 'false');
    wing.style.transform = ''; // Clear inline transform
  });
  
  initializeGame();
}

// Share functionality
async function shareResult() {
  const selectedResult = gameState.selectedWingIndex >= 0 
    ? gameState.wings[gameState.selectedWingIndex] 
    : null;
    
  if (!selectedResult) return;
  
  const shareData = {
    title: 'ペガサスの羽みくじ',
    text: `${selectedResult.name}が出ました！${selectedResult.message}`,
    url: window.location.href
  };
  
  try {
    if (navigator.share && navigator.canShare(shareData)) {
      await navigator.share(shareData);
    } else {
      // Fallback to clipboard
      const shareText = `${shareData.title}\n${shareData.text}\n${shareData.url}`;
      await navigator.clipboard.writeText(shareText);
      
      // Show temporary feedback
      const btn = $('#shareButton');
      const originalText = btn.textContent;
      btn.textContent = 'コピー済み！';
      setTimeout(() => {
        btn.textContent = originalText;
      }, 2000);
    }
  } catch (error) {
    console.warn('Share failed:', error);
  }
}

// Event Handlers
function handleWingClick(event) {
  const wingButton = event.currentTarget;
  
  // Additional safety check
  if (wingButton.classList.contains('wing--revealed')) {
    return;
  }
  
  const wingIndexStr = wingButton.getAttribute('data-wing-index');
  const wingIndex = parseInt(wingIndexStr, 10);
  
  if (isNaN(wingIndex)) {
    return;
  }
  
  revealWing(wingIndex);
}

function handleKeyDown(event) {
  if (event.target.classList.contains('wing') && 
      (event.key === 'Enter' || event.key === ' ')) {
    event.preventDefault();
    event.target.click();
  }
}

// Initialize Event Listeners
function bindEvents() {
  // Wing clicks - delegate to individual wing buttons
  const gameBoard = $('#gameBoard');
  if (gameBoard) {
    gameBoard.addEventListener('click', (event) => {
      const wingButton = event.target.closest('.wing');
      if (wingButton && !wingButton.disabled && !wingButton.classList.contains('wing--revealed')) {
        handleWingClick({ currentTarget: wingButton });
      }
    });
  }
  
  // Keyboard navigation
  document.addEventListener('keydown', handleKeyDown);
  
  // Control buttons
  const playAgainBtn = $('#playAgain');
  const shareBtn = $('#shareButton');
  
  if (playAgainBtn) {
    playAgainBtn.addEventListener('click', resetGame);
  }
  if (shareBtn) {
    shareBtn.addEventListener('click', shareResult);
  }
}

// Cosmic Background Effects
function createCosmicStars() {
  const starsContainer = $('#cosmicStars');
  if (!starsContainer) return;
  
  const starCount = window.innerWidth < 768 ? 50 : 100;
  starsContainer.innerHTML = '';
  
  for (let i = 0; i < starCount; i++) {
    const star = document.createElement('div');
    star.className = 'cosmic-star';
    
    // Random size
    const sizeClass = Math.random() < 0.7 ? 'small' : (Math.random() < 0.8 ? 'medium' : 'large');
    star.classList.add(sizeClass);
    
    // Random position
    star.style.left = Math.random() * 100 + '%';
    star.style.top = Math.random() * 100 + '%';
    
    // Random animation delay
    star.style.animationDelay = Math.random() * 3 + 's';
    
    starsContainer.appendChild(star);
  }
}

function createCosmicParticles() {
  const particlesContainer = $('#cosmicParticles');
  if (!particlesContainer) return;
  
  const particleCount = window.innerWidth < 768 ? 15 : 25;
  
  // Create initial particles
  for (let i = 0; i < particleCount; i++) {
    createCosmicParticle(particlesContainer, true);
  }
  
  // Continuously create new particles
  setInterval(() => {
    if (particlesContainer.children.length < particleCount * 2) {
      createCosmicParticle(particlesContainer, false);
    }
  }, 800);
}

function createCosmicParticle(container, isInitial) {
  const particle = document.createElement('div');
  particle.className = 'cosmic-particle';
  
  // Random horizontal position
  particle.style.left = Math.random() * 100 + '%';
  
  if (isInitial) {
    // For initial particles, place them randomly on screen
    particle.style.top = Math.random() * 100 + '%';
    particle.style.animationDelay = Math.random() * 8 + 's';
  } else {
    // For new particles, start from bottom
    particle.style.top = '100vh';
  }
  
  // Random animation duration
  particle.style.animationDuration = (6 + Math.random() * 4) + 's';
  
  container.appendChild(particle);
  
  // Remove particle after animation
  setTimeout(() => {
    if (container.contains(particle)) {
      container.removeChild(particle);
    }
  }, 10000);
}

// Enhanced wing reveal with cosmic effects
function enhanceWingReveal(wingButton) {
  // Add cosmic pulse effect
  wingButton.classList.add('wing--success');
  
  // Ensure the wing button has relative positioning for sparkles
  const originalPosition = wingButton.style.position;
  if (!originalPosition || originalPosition === 'static') {
    wingButton.style.position = 'relative';
  }
  
  // Create temporary sparkle effect
  const sparkles = document.createElement('div');
  sparkles.className = 'cosmic-sparkles';
  sparkles.style.position = 'absolute';
  sparkles.style.top = '0';
  sparkles.style.left = '0';
  sparkles.style.width = '100%';
  sparkles.style.height = '100%';
  sparkles.style.pointerEvents = 'none';
  sparkles.style.zIndex = '10';
  sparkles.style.overflow = 'visible'; // Allow sparkles to extend beyond button
  
  for (let i = 0; i < 8; i++) {
    const sparkle = document.createElement('div');
    sparkle.style.position = 'absolute';
    sparkle.style.width = '4px';
    sparkle.style.height = '4px';
    sparkle.style.background = Math.random() < 0.5 ? '#2ea7e0' : '#00a8b5';
    sparkle.style.borderRadius = '50%';
    sparkle.style.left = Math.random() * 100 + '%';
    sparkle.style.top = Math.random() * 100 + '%';
    sparkle.style.animation = `sparkleOut 1s ease-out forwards`;
    sparkle.style.animationDelay = (i * 0.1) + 's';
    sparkles.appendChild(sparkle);
  }
  
  wingButton.appendChild(sparkles);
  
  setTimeout(() => {
    if (wingButton.contains(sparkles)) {
      wingButton.removeChild(sparkles);
    }
  }, 2000);
}

// Add sparkle animation to CSS dynamically
function addCosmicAnimations() {
  const style = document.createElement('style');
  style.textContent = `
    @keyframes sparkleOut {
      0% {
        opacity: 1;
        transform: scale(0) rotate(0deg);
      }
      100% {
        opacity: 0;
        transform: scale(1.5) rotate(180deg) translateY(-20px);
      }
    }
  `;
  document.head.appendChild(style);
}

// Initialization
function initApp() {
  // Initialize cosmic background
  addCosmicAnimations();
  createCosmicStars();
  createCosmicParticles();
  
  // Handle window resize for responsive cosmic effects
  window.addEventListener('resize', () => {
    createCosmicStars();
  });
  
  loadStats();
  initializeGame();
  bindEvents();
  
  // Announce game ready for screen readers
  setTimeout(() => {
    const announcement = document.createElement('div');
    announcement.setAttribute('aria-live', 'polite');
    announcement.textContent = 'ペガサスの羽みくじが準備できました。5枚の羽から1枚を選んでください。';
    announcement.className = 'sr-only';
    document.body.appendChild(announcement);
    
    setTimeout(() => {
      if (document.body.contains(announcement)) {
        document.body.removeChild(announcement);
      }
    }, 1000);
  }, 500);
}

// Start the app when DOM is loaded
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}

// Export for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    CONFIG,
    gameState,
    choiceWeighted,
    shuffle,
    generateResultPool
  };
}
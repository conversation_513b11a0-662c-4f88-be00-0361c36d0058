/* CSS Variables for Space Theme */
:root {
  /* Cosmic Color Palette */
  --color-cosmic-blue: #2ea7e0;    /* 水色 */
  --color-cosmic-teal: #00a8b5;    /* 青緑色 */
  --color-cosmic-black: #0a0a0a;   /* 宇宙の黒 */
  --color-cosmic-white: #ffffff;   /* 星の白 */
  --color-cosmic-dark: #1a1a2e;    /* 深宇宙 */
  --color-cosmic-glow: rgba(46, 167, 224, 0.3); /* 水色の光 */
  
  /* Legacy mappings */
  --color-bg: var(--color-cosmic-dark);
  --color-primary: var(--color-cosmic-blue);
  --color-text: var(--color-cosmic-white);
  --color-wing-back: rgba(255, 255, 255, 0.1);
  --color-wing-border: var(--color-cosmic-teal);
  --color-success: var(--color-cosmic-teal);
  --color-neutral: rgba(255, 255, 255, 0.7);
  --color-shadow: rgba(46, 167, 224, 0.2);
  
  --font-family: system-ui, -apple-system, "Segoe UI", "Noto Sans JP", sans-serif;
  
  --transition-duration: 0.5s;
  --transition-easing: cubic-bezier(0.22, 1, 0.36, 1);
  
  --wing-mobile-w: 120px;
  --wing-mobile-h: 180px;
  --wing-desktop-w: 160px;
  --wing-desktop-h: 220px;
}

/* Reset & Base */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family);
  background: var(--color-cosmic-black);
  color: var(--color-text);
  line-height: 1.6;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  overflow-x: hidden;
}

/* Cosmic Background */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url('../assets/universe.jpg');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  z-index: -2;
  filter: brightness(0.4) contrast(1.2);
}

body::after {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(ellipse at center, transparent 0%, rgba(10, 10, 10, 0.6) 70%, rgba(10, 10, 10, 0.9) 100%);
  z-index: -1;
  pointer-events: none;
}

/* Cosmic Stars Background */
.cosmic-stars {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
}

.cosmic-star {
  position: absolute;
  background: var(--color-cosmic-white);
  border-radius: 50%;
  animation: twinkle 3s infinite ease-in-out;
}

.cosmic-star.small {
  width: 1px;
  height: 1px;
}

.cosmic-star.medium {
  width: 2px;
  height: 2px;
}

.cosmic-star.large {
  width: 3px;
  height: 3px;
  box-shadow: 0 0 6px var(--color-cosmic-blue);
}

@keyframes twinkle {
  0%, 100% { 
    opacity: 0.3;
    transform: scale(1);
  }
  50% { 
    opacity: 1;
    transform: scale(1.2);
  }
}

/* Cosmic particle effect */
.cosmic-particles {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  pointer-events: none;
}

.cosmic-particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--color-cosmic-teal);
  border-radius: 50%;
  animation: float 8s infinite linear;
  opacity: 0.6;
}

@keyframes float {
  0% {
    transform: translateY(100vh) translateX(0px);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-10vh) translateX(50px);
    opacity: 0;
  }
}

/* Header */
.header {
  text-align: center;
  margin: 2rem 0;
  padding: 2rem;
  background: rgba(26, 26, 46, 0.8);
  border-radius: 20px;
  border: 1px solid var(--color-cosmic-teal);
  box-shadow: 0 8px 32px var(--color-cosmic-glow);
  backdrop-filter: blur(10px);
}

.title {
  font-size: clamp(1.8rem, 4vw, 2.5rem);
  background: linear-gradient(135deg, var(--color-cosmic-blue) 0%, var(--color-cosmic-teal) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  text-shadow: 0 0 20px var(--color-cosmic-glow);
}

.subtitle {
  font-size: clamp(1rem, 2.5vw, 1.2rem);
  color: var(--color-cosmic-white);
  margin: 0 0 1rem 0;
  opacity: 0.9;
}

.howto {
  font-size: 0.9rem;
  color: var(--color-cosmic-white);
  margin: 0;
  padding: 0.5rem 1rem;
  background: rgba(46, 167, 224, 0.2);
  border: 1px solid var(--color-cosmic-blue);
  border-radius: 20px;
  display: inline-block;
  backdrop-filter: blur(5px);
}

/* Main Game Area */
.main {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 1200px;
  position: relative;
  z-index: 1;
}

/* Game Board */
.board {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 20px;
  margin-bottom: 2rem;
  justify-items: center;
  background: rgba(26, 26, 46, 0.3);
  border-radius: 20px;
  border: 1px solid rgba(46, 167, 224, 0.3);
  backdrop-filter: blur(10px);
}

@media (min-width: 768px) {
  .board {
    grid-template-columns: repeat(5, 1fr);
    gap: 24px;
    padding: 24px;
  }
}

/* Wing Card - Cosmic Design */
.wing {
  perspective: 1000px;
  width: var(--wing-mobile-w);
  height: var(--wing-mobile-h);
  cursor: pointer;
  border: none;
  background: none;
  padding: 0;
  transition: transform 0.2s ease, filter 0.2s ease;
  position: relative;
  display: inline-block;
  vertical-align: top;
}

@media (min-width: 768px) {
  .wing {
    width: var(--wing-desktop-w);
    height: var(--wing-desktop-h);
  }
}

.wing {
  transform: translateY(10px);
  transition: transform 0.3s ease;
}

.wing:hover:not(:disabled):not(.wing--revealed) {
  transform: translateY(-10px) !important;
  filter: drop-shadow(0 10px 20px var(--color-cosmic-glow));
}

.wing:focus {
  outline: 3px solid var(--color-cosmic-blue);
  outline-offset: 4px;
}

.wing:disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

/* Keep revealed wings elevated */
.wing--revealed {
  transform: translateY(0px) !important;
  filter: drop-shadow(0 10px 20px var(--color-cosmic-glow));
}

.wing--revealed:hover {
  transform: translateY(0px) !important;
}

.wing__inner {
  width: 100%;
  height: 100%;
  position: relative;
  transform-style: preserve-3d;
  transition: transform var(--transition-duration) var(--transition-easing);
}

.wing--revealed .wing__inner {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  transition: box-shadow 0.2s ease;
  overflow: hidden;
}

/* Card Front - Mystical Design */
.card-front {
  background: linear-gradient(135deg, 
    rgba(46, 167, 224, 0.1) 0%, 
    rgba(0, 168, 181, 0.1) 50%, 
    rgba(26, 26, 46, 0.2) 100%);
  border: 2px solid var(--color-cosmic-teal);
  box-shadow: 
    0 8px 32px rgba(0, 168, 181, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
}

.card-front::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, var(--color-cosmic-blue), var(--color-cosmic-teal), var(--color-cosmic-blue));
  border-radius: 16px;
  z-index: -1;
  animation: borderGlow 3s ease-in-out infinite alternate;
}

@keyframes borderGlow {
  from {
    opacity: 0.5;
    filter: blur(0px);
  }
  to {
    opacity: 1;
    filter: blur(1px);
  }
}

.card-back {
  background: linear-gradient(135deg, 
    var(--color-cosmic-blue) 0%, 
    var(--color-cosmic-teal) 100%);
  color: white;
  transform: rotateY(180deg);
  padding: 1rem;
  text-align: center;
  box-shadow: 
    0 8px 32px rgba(46, 167, 224, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

.wing-icon {
  width: 70px;
  height: 70px;
  color: var(--color-cosmic-blue);
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 0 10px var(--color-cosmic-glow));
}

@media (min-width: 768px) {
  .wing-icon {
    width: 90px;
    height: 90px;
  }
}

.result-text {
  font-size: clamp(1.2rem, 3vw, 1.8rem);
  font-weight: 600;
  margin-bottom: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.result-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 0.5rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.result-message {
  font-size: 0.8rem;
  opacity: 0.9;
  line-height: 1.4;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* Result-specific cosmic styles */
.result--daikichi .card-back { 
  background: linear-gradient(135deg, #00a8b5 0%, #2ea7e0 100%);
}
.result--kichi .card-back { 
  background: linear-gradient(135deg, #2ea7e0 0%, #00a8b5 100%);
}
.result--shokichi .card-back { 
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(46, 167, 224, 0.3) 100%);
}
.result--tonkotsu .card-back { 
  background: linear-gradient(135deg, #2ea7e0 0%, #8b4513 50%, #00a8b5 100%);
}
.result--kaedama .card-back { 
  background: linear-gradient(135deg, #00a8b5 0%, #ff6b35 50%, #2ea7e0 100%);
}

/* Controls */
.controls {
  display: flex;
  gap: 1rem;
  margin: 2rem 0;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-family: inherit;
  min-width: 120px;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: 3px solid var(--color-cosmic-blue);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.btn--primary {
  background: linear-gradient(135deg, var(--color-cosmic-blue) 0%, var(--color-cosmic-teal) 100%);
  color: white;
  border: 1px solid var(--color-cosmic-teal);
  box-shadow: 0 4px 15px var(--color-cosmic-glow);
}

.btn--primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.btn--primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-cosmic-teal) 0%, var(--color-cosmic-blue) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px var(--color-cosmic-glow);
}

.btn--primary:hover::before {
  left: 100%;
}

.btn--secondary {
  background: transparent;
  color: var(--color-cosmic-blue);
  border: 2px solid var(--color-cosmic-blue);
  backdrop-filter: blur(10px);
}

.btn--secondary:hover:not(:disabled) {
  background: var(--color-cosmic-blue);
  color: white;
  box-shadow: 0 4px 15px var(--color-cosmic-glow);
}

/* Toast - Cosmic Style */
.toast {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, 
    rgba(26, 26, 46, 0.95) 0%, 
    rgba(46, 167, 224, 0.1) 100%);
  border: 2px solid var(--color-cosmic-teal);
  border-radius: 20px;
  padding: 2rem;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.4),
    0 0 40px var(--color-cosmic-glow);
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
  max-width: 90vw;
  width: 350px;
  backdrop-filter: blur(20px);
}

.toast--visible {
  opacity: 1;
  visibility: visible;
  transform: translate(-50%, -50%) scale(1);
}

.toast__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.toast__icon {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  color: var(--color-cosmic-blue);
  filter: drop-shadow(0 0 10px var(--color-cosmic-glow));
}

.toast__message {
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--color-cosmic-white);
  text-shadow: 0 0 10px var(--color-cosmic-glow);
}

/* Toast color variants */
.toast--success .toast__icon { 
  color: var(--color-cosmic-teal);
  filter: drop-shadow(0 0 10px rgba(0, 168, 181, 0.5));
}
.toast--primary .toast__icon { 
  color: var(--color-cosmic-blue);
  filter: drop-shadow(0 0 10px var(--color-cosmic-glow));
}
.toast--neutral .toast__icon { 
  color: var(--color-cosmic-white);
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.3));
}

/* Stats */
.stats {
  display: flex;
  gap: 2rem;
  font-size: 0.9rem;
  color: var(--color-cosmic-white);
  margin: 1rem 0;
  justify-content: center;
  flex-wrap: wrap;
  background: rgba(26, 26, 46, 0.6);
  padding: 1rem 2rem;
  border-radius: 15px;
  border: 1px solid rgba(46, 167, 224, 0.3);
  backdrop-filter: blur(10px);
}

/* Footer */
.footer {
  margin-top: 2rem;
  text-align: center;
  color: var(--color-cosmic-white);
  font-size: 0.8rem;
  opacity: 0.7;
  padding: 1rem;
  background: rgba(26, 26, 46, 0.5);
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

/* Enhanced Animations */
@keyframes flipIn {
  from {
    transform: scale(0.8) rotateY(-90deg);
    opacity: 0;
    filter: brightness(0.5);
  }
  to {
    transform: scale(1) rotateY(0deg);
    opacity: 1;
    filter: brightness(1);
  }
}

@keyframes cosmicPulse {
  0%, 100% {
    box-shadow: 0 0 20px var(--color-cosmic-glow);
    filter: drop-shadow(0 10px 20px var(--color-cosmic-glow));
  }
  50% {
    box-shadow: 0 0 40px var(--color-cosmic-glow), 0 0 60px rgba(0, 168, 181, 0.3);
    filter: drop-shadow(0 15px 30px var(--color-cosmic-glow));
  }
}

.wing {
  animation: flipIn 0.8s ease backwards;
}

.wing:nth-child(1) { animation-delay: 0ms; }
.wing:nth-child(2) { animation-delay: 100ms; }
.wing:nth-child(3) { animation-delay: 200ms; }
.wing:nth-child(4) { animation-delay: 300ms; }
.wing:nth-child(5) { animation-delay: 400ms; }

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  * {
    transition: none !important;
    animation: none !important;
  }
  
  .wing__inner {
    transition: none;
  }
  
  .wing--revealed .wing__inner {
    transform: none;
  }
  
  .card-back {
    display: none;
  }
  
  .wing--revealed .card-back {
    display: flex;
  }
  
  /* Maintain hover position even without animation */
  .wing:hover:not(:disabled):not(.wing--revealed) {
    transform: none;
  }
  
  .wing--revealed {
    transform: translateY(0px) !important;
  }
}

/* Loading state */
.loading .wing {
  pointer-events: none;
  opacity: 0.7;
}

/* Success states for better visual feedback */
.wing--success {
  animation: cosmicPulse 2s ease-in-out infinite;
  /* Override any transform animation conflicts */
  transform: translateY(0px) !important;
}

/* Ensure revealed wings maintain their position and effects */
.wing--revealed.wing--success {
  transform: translateY(0px) !important;
  filter: drop-shadow(0 10px 20px var(--color-cosmic-glow));
}

/* Mobile optimizations */
@media only screen and (max-width: 1024px) {
  body::before {
    background-attachment: scroll;
  }
}

/* Utility classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  border: 0;
}
<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ペガサスの羽みくじ - デバッグ版</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
        .debug { background: #fff; padding: 20px; margin: 10px 0; border-radius: 5px; }
        .wing { width: 100px; height: 150px; margin: 10px; background: #46A3FF; color: white; border: none; cursor: pointer; border-radius: 8px; }
        .wing:hover { background: #3d8bdb; }
        .board { display: flex; flex-wrap: wrap; }
        .console { background: #000; color: #0f0; padding: 10px; font-family: monospace; height: 200px; overflow-y: scroll; }
    </style>
</head>
<body>
    <h1>ペガサスの羽みくじ - デバッグ版</h1>
    
    <div class="debug">
        <h2>ゲーム状態</h2>
        <p>Phase: <span id="debugPhase">-</span></p>
        <p>Selected Wing: <span id="debugSelectedWing">-</span></p>
        <p>Wings Generated: <span id="debugWingsCount">-</span></p>
    </div>
    
    <div class="debug">
        <h2>ゲームボード</h2>
        <div class="board" id="debugBoard">
            <!-- Wings will be generated here -->
        </div>
        <button onclick="debugInitGame()">ゲーム初期化</button>
        <button onclick="debugLogState()">状態をログ出力</button>
    </div>
    
    <div class="debug">
        <h2>コンソールログ</h2>
        <div class="console" id="debugConsole"></div>
        <button onclick="clearDebugConsole()">ログクリア</button>
    </div>

    <script>
        // Override console.log for debug display
        const originalLog = console.log;
        const debugConsole = document.getElementById('debugConsole');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (debugConsole) {
                const logEntry = document.createElement('div');
                logEntry.textContent = args.map(arg => 
                    typeof arg === 'object' ? JSON.stringify(arg) : String(arg)
                ).join(' ');
                debugConsole.appendChild(logEntry);
                debugConsole.scrollTop = debugConsole.scrollHeight;
            }
        };
        
        function clearDebugConsole() {
            debugConsole.innerHTML = '';
        }
        
        function updateDebugInfo() {
            document.getElementById('debugPhase').textContent = gameState.phase;
            document.getElementById('debugSelectedWing').textContent = gameState.selectedWingIndex;
            document.getElementById('debugWingsCount').textContent = gameState.wings.length;
        }
        
        function debugInitGame() {
            console.log('=== DEBUG: Manual game initialization ===');
            initializeGame();
            updateDebugInfo();
            
            // Create simple debug wings
            const board = document.getElementById('debugBoard');
            board.innerHTML = '';
            
            for (let i = 0; i < 5; i++) {
                const wing = document.createElement('button');
                wing.className = 'wing';
                wing.textContent = `羽 ${i + 1}`;
                wing.onclick = () => {
                    console.log(`Debug wing ${i} clicked`);
                    revealWing(i);
                    updateDebugInfo();
                };
                board.appendChild(wing);
            }
        }
        
        function debugLogState() {
            console.log('=== GAME STATE DEBUG ===');
            console.log('gameState:', gameState);
            console.log('CONFIG:', CONFIG);
            console.log('DOM elements check:');
            console.log('- gameBoard:', document.getElementById('gameBoard'));
            console.log('- playAgain:', document.getElementById('playAgain'));
            console.log('- shareButton:', document.getElementById('shareButton'));
        }
    </script>
    
    <!-- Load the main game script -->
    <script src="scripts/app.js"></script>
    
    <script>
        // Auto-initialize debug version
        setTimeout(() => {
            debugInitGame();
            console.log('Debug page loaded and initialized');
        }, 100);
    </script>
</body>
</html>
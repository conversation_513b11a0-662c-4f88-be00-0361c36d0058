# ペガサスの羽みくじ - 宇宙観デザイン

宇宙の神秘と美しさに包まれた、5枚のペガサスの羽をタップして運勢をチェックする宇宙観ミニゲームです。

## 🎮 ゲーム概要

- **プレイ時間**: 1プレイ10秒以内
- **対象**: FPの見込み客・来訪者、セミナー参加者
- **デバイス**: スマートフォン・タブレット・PC対応

## ✨ 特徴

### 🌌 宇宙観デザイン
- **宇宙背景**: 本格的な宇宙画像と星空アニメーション
- **コズミックカラー**: 水色(#2ea7e0)と青緑色(#00a8b5)の美しいグラデーション
- **星空エフェクト**: ランダムに瞬く星々とコズミックパーティクル
- **光る境界線**: カードのグロー効果とパルスアニメーション
- **スパークルエフェクト**: カードをめくった時の宇宙的な演出

### 🎮 ゲーム機能
- **5つの結果**: 小吉/吉/大吉/豚骨/替え玉
- **確率設定**: 
  - 小吉: 36%
  - 吉: 32%  
  - 大吉: 12%
  - 豚骨: 10%
  - 替え玉: 10%
- **3Dフリップアニメーション**: CSS 3D Transformを使用
- **レスポンシブデザイン**: モバイルファースト
- **アクセシビリティ対応**: キーボード操作・スクリーンリーダー対応
- **オフライン動作**: 通信不要

## 🚀 使用方法

1. ブラウザで `index.html` を開く
2. 5枚の羽から1枚をクリック（またはタップ）
3. 結果を確認
4. 「もう一度」ボタンで再プレイ

## 📱 対応ブラウザ

- **iOS**: Safari 15+
- **Android**: Chrome 105+
- **Desktop**: Chrome/Edge/Firefox/Safari 最新2メジャーバージョン

## ⌨️ キーボード操作

- `Tab`: 羽の選択
- `Enter` / `Space`: 羽を選択
- `Tab`: ボタンの移動

## 🎨 カスタマイズ

### 色の変更

`styles/main.css` の `:root` セクションで色を変更できます:

```css
:root {
  --color-primary: #46A3FF;    /* メインカラー */
  --color-success: #16a34a;    /* 成功色（大吉） */
  --color-neutral: #6b7280;    /* ニュートラル色 */
}
```

### 確率の調整

`scripts/app.js` の `CONFIG.RESULTS` で各結果の確率を変更できます:

```javascript
RESULTS: {
  DAIKICHI: { name: '大吉', weight: 0.12, ... },
  // weight の値を変更（合計1.0になるように調整）
}
```

## 📊 統計機能

- プレイ回数のカウント
- 最高結果の記録
- ローカルストレージに保存（個人識別情報なし）

## 🔧 ファイル構成

```
pegasus-wing-fortune/
├── index.html              # メインHTMLファイル
├── styles/
│   └── main.css           # スタイルシート
├── scripts/
│   └── app.js            # ゲームロジック
├── assets/
│   └── icons/
│       ├── wing.svg          # 羽のアイコン
│       ├── fortune-star.svg  # 星のアイコン
│       └── fortune-ramen.svg # ラーメンのアイコン
└── README.md              # このファイル
```

## 🌐 デプロイ方法

1. **静的ホスティング**: 全ファイルをWebサーバーにアップロード
2. **GitHub Pages**: GitHubリポジトリのPages機能を使用
3. **Netlify/Vercel**: ドラッグ&ドロップでデプロイ

## ⚡ パフォーマンス目標

- **初回操作準備**: 800ms以内
- **JSバンドルサイズ**: 20KB以下（gzip圧縮後）
- **60fps相当のアニメーション**

## 🔒 プライバシー

- クッキー使用なし
- 外部通信なし
- 個人識別情報の収集なし
- ローカルストレージに最小限の統計のみ保存

## 🎯 将来の拡張予定

- 結果に応じたミニTips表示
- シェア用OG画像の動的生成
- 多言語対応（英語・中国語）
- サウンド効果（オプション）

## 📄 ライセンス

© Space Cowboy Inc.

## 🐛 トラブルシューティング

### アニメーションが動かない場合

1. ブラウザが CSS 3D Transform に対応しているか確認
2. `prefers-reduced-motion` の設定を確認

### 統計が保存されない場合

1. ブラウザがローカルストレージに対応しているか確認
2. プライベートブラウジングモードでないか確認

### レスポンシブが効かない場合

1. viewport meta タグが設定されているか確認
2. CSS Grid のサポートを確認
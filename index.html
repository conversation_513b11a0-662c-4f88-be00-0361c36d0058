<!DOCTYPE html>
<html lang="ja">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>株式会社スペースカウボーイ | 見上げる空から、つながる宇宙へ</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- ファビコン -->
    <link rel="icon" type="image/x-icon" href="static/img/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="static/img/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="static/img/icon-32x32.png">
    <link rel="apple-touch-icon" sizes="180x180" href="static/img/apple-touch-icon-180x180.png">
    
    <!-- OGP設定（検索結果・SNSシェア用） -->
    <meta property="og:title" content="株式会社スペースカウボーイ | 見上げる空から、つながる宇宙へ">
    <meta property="og:description" content="宇宙事業部と映像・Web制作のクリエイティブ事業部で、宇宙から地上まで表現と技術で課題解決を行う福岡の企業です。">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.space-cowboy.jp/?v=2">
    <meta property="og:image" content="https://www.space-cowboy.jp/static/img/ogp1200x630.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:type" content="image/png">
    <meta property="og:site_name" content="株式会社スペースカウボーイ">
    <meta property="og:locale" content="ja_JP">
    
    <!-- Twitter Card設定 -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@cutkey5">
    
    <style>
        body {
            font-family: 'Noto Sans JP', sans-serif;
        }

        .montserrat-regular {
            font-family: "Montserrat", sans-serif;
            font-weight: 420;
            font-style: normal;
        }

        .montserrat-bold {
            font-family: "Montserrat", sans-serif;
            font-weight: 560;
            font-style: normal;
        }
    </style>
</head>

<body class="bg-gray-900 text-white">
    <!-- 星空背景 -->
    <div class="stars" id="stars"></div>

    <!-- Navigation Section -->
    <nav class="fixed top-0 w-full z-50">
        <div class="max-w-7xl mx-auto px-6 py-6 md:px-12 py-12">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2 text-lg text-white montserrat-regular tracking-wide">
                    <span>SPACE COWBOY</span>
                </div>

                <div class="md:hidden">
                    <button id="menu-button" class="focus:outline-none text-white p-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16m-7 6h7"></path>
                        </svg>
                    </button>
                </div>
                <div class="hidden md:flex space-x-10">
                    <a href="company.html"
                        class="text-sm font-light text-white hover:text-blue-400 transition-colors montserrat-regular">COMPANY</a>
                    <a href="service.html"
                        class="text-sm font-light text-white hover:text-blue-400 transition-colors montserrat-regular">SERVICE</a>
                    <a href="contact.html"
                        class="text-sm font-light text-white hover:text-blue-400 transition-colors montserrat-regular">CONTACT</a>
                    <a href="pegasus-wing-fortune/index.html"
                        class="text-sm font-light text-white hover:text-blue-400 transition-colors montserrat-regular">🎮 GAME</a>
                </div>
                <!-- Mobile menu -->
                <div id="mobile-menu"
                    class="hidden md:hidden absolute top-full left-0 w-full bg-black/85 backdrop-blur-md border-t border-gray-700/50 shadow-2xl z-50">
                    <div class="flex flex-col px-6 py-8">
                        <a href="company.html"
                            class="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                            <span>COMPANY</span>
                            <svg class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </a>
                        <a href="service.html"
                            class="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                            <span>SERVICE</span>
                            <svg class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </a>
                        <a href="contact.html"
                            class="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                            <span>CONTACT</span>
                            <svg class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </a>
                        <a href="pegasus-wing-fortune/index.html"
                            class="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular">
                            <span>🎮 FORTUNE GAME</span>
                            <svg class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    <!-- パララックス背景 -->
    <div class="parallax-bg" id="parallax-bg"></div>

    <!-- Heroセクション本体 -->
    <div class="parallax-wrapper">
        <section
            class="min-h-screen flex flex-col justify-center items-start text-left px-6 pt-32 pb-16 md:px-12 md:pt-48 md:pb-24 relative z-10">
            <div class="max-w-4xl animate-fade-in">
                <h1 id="hero-title" class="text-6xl md:text-8xl text-white montserrat-bold mb-24"
                    style="letter-spacing: 0.1em;">
                    <div>
                        <span>HELLO</span><br>
                        <span>SPACEY-FUN</span>
                    </div>
                </h1>

                <div class="max-w-2xl text-left leading-loose mb-12 text-white">
                    <p id="hero-subtitle-1" class="text-2xl md:text-3xl font-medium text-white mb-8">
                        見上げよう空を、<br>
                        つながろう宇宙で。
                    </p>
                    <p id="hero-subtitle-2" class="text-sm md:text-base font-light text-gray-400 leading-loose">
                        日々の忙しさのなか、ふと空を見上げて心を落ち着かせる。<br>
                        そして夢を描き、未来へ思いを馳せる。<br>
                        そんな瞬間で、人と人とをつなげていきたい。<br><br>
                        私たち自身も、宇宙という挑戦に本気で向き合っています。<br>
                        だからこそ、挑戦するすべての人々に、心から寄り添います。<br>
                        あなたの課題にも、SPACEYな視野でお応えします。<br><br>
                    </p>
                </div>

                <a href="contact.html" class="hero-cta-button inline-block">
                    <p class="flex items-center justify-center">
                        <span class="text-2xl mr-2">🚀</span>まずはご相談ください
                    </p>
                </a>


            </div>
        </section>
    </div>

    <!-- Photo Section -->
    <div class="px-0 py-0 bg-black">
        <div class="max-w-full mx-auto grid grid-cols-1 md:grid-cols-2">
            <div>
                <img src="static/img/hero_image_01.jpg" alt="Hero Image 1" class="w-full h-[20vh] object-cover" loading="lazy" />
            </div>
            <div>
                <img src="static/img/hero_image_02.jpg" alt="Hero Image 2" class="w-full h-[20vh] object-cover" loading="lazy" />
            </div>
        </div>
    </div>

    <!-- SERVICE Section -->
    <section id="service" class="bg-gray-900 text-white">
        <div class="max-w-6xl mx-auto px-6 pt-24 pb-24 md:px-12 md:pt-36 md:pb-24">
            <div class="flex justify-between items-center mb-16">
                <h2 class="text-4xl md:text-6xl montserrat-bold">SERVICE</h2>
                <a href="service.html" class="flex items-center text-blue-300 hover:text-blue-100 transition">
                    <span class="text-sm md:text-base montserrat-regular">VIEW MORE &gt;</span>
                </a>
            </div>
            <div class="mb-16 md:flex md:items-start md:gap-12">
                <!-- 左側：テキスト -->
                <div class="md:w-1/2 grid grid-rows-[1fr_auto] gap-4">
                    <h3 class="text-xl md:text-2xl mb-4">
                        宇宙のことも、<br>
                        クリエイティブのことも。
                    </h3>
                    <p class="text-xs md:text-sm max-w-3xl mb-12 font-light text-gray-400 leading-loose">
                        株式会社スペースカウボーイは、「宇宙事業部」と「クリエイティブ事業部」の2つの軸で事業を展開。
                        宇宙産業におけるコンテンツ制作・情報発信・エンジニアリング・プロダクト構想から、
                        地上のクリエイティブ課題まで幅広く支援しています。
                    </p>

                    <div>
                        <a href="service.html" class="hero-cta-button inline-block">
                            <span class="flex items-center justify-center">
                                <span class="text-2xl mr-2">🌝</span>サービスの詳細
                            </span>
                        </a>
                    </div>

                </div>
                <!-- 右側：画像 -->
                <div class="mt-8 md:mt-0 md:w-1/2">
                    <img src="static/img/service_overview.png" alt="サービス紹介画像"
                        class="w-full h-auto opacity-0 transition-opacity duration-1000" loading="lazy"
                        onload="this.classList.remove('opacity-0'); this.classList.add('opacity-100');" />
                </div>
            </div>

            <!-- ライン -->
            <hr class="border-t border-gray-700 my-16" />

            <!-- Divisions -->
            <div class="max-w-6xl mx-auto md:pb-12">
                <!-- 宇宙事業部 -->
                <div class="mb-8">
                    <div class="md:flex md:gap-12">
                        <!-- 左カラム -->
                        <div class="md:w-3/5 mb-8 md:mb-0">
                            <div class="flex items-center mb-4">
                                <span class="text-3xl mr-3">🛰️</span>
                                <h4 class="text-xl md:text-2xl font-bold">宇宙事業部</h3>
                            </div>
                            <p class="text-blue-300 text-sm md:text-base mb-6 font-medium">宇宙と社会をつなぐ</p>
                            <p class="text-gray-300 text-sm leading-loose font-light">
                                宇宙に関する知識や人とのつながりを深めながら、
                                コンテンツ制作、情報発信、アプリ開発、プロダクト企画など、宇宙関連のあらゆる領域で幅広く支援。
                                民間・行政・大学をつなぐハブとしての機能を担います。
                            </p>
                            <a href="service.html#space-division" class="arrow-link">
                                <span class="text-sm">詳しく見る</span>
                                <span class="arrow"></span>
                            </a>
                        </div>

                        <!-- 右カラム -->
                        <div class="md:w-2/5">
                            <div class="flex items-center mb-8">
                                <div class="w-16 border-t border-gray-500"></div>
                                <h4 class="text-sm font-semibold text-blue-200 ml-4 whitespace-nowrap">提供メニュー</h4>
                            </div>

                            <ul class="flex flex-wrap gap-2">
                                <li>
                                    <span
                                        class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                        広報戦略設計
                                    </span>
                                </li>
                                <li>
                                    <span
                                        class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                        映像・動画制作
                                    </span>
                                </li>
                                <li>
                                    <span
                                        class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                        セミナー・イベント企画
                                    </span>
                                </li>
                                <li>
                                    <span
                                        class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                        Webアプリ開発
                                    </span>
                                </li>
                                <li>
                                    <span
                                        class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                        衛星データ利活用
                                    </span>
                                </li>
                                <li>
                                    <span
                                        class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                        宇宙コンテンツ企画
                                    </span>
                                </li>
                            </ul>

                        </div>


                    </div>
                </div>

                <!-- ライン -->
                <hr class="border-t border-gray-700 my-16" />

                <!-- クリエイティブ戦略部 -->
                <div class>
                    <div class="md:flex md:gap-12">
                        <!-- 左カラム -->
                        <div class="md:w-3/5 mb-8 md:mb-0">
                            <div class="flex items-center mb-4">
                                <span class="text-3xl mr-3">🎬</span>
                                <h3 class="text-xl md:text-2xl font-bold">クリエイティブ事業部</h3>
                            </div>
                            <p class="text-purple-300 text-sm md:text-base mb-6 font-medium">「伝える」をデザインする</p>
                            <p class="text-gray-300 text-sm leading-loose font-light">
                                宇宙に限らず、名刺・チラシ・映像・ロゴ・SNS・Webなど、
                                あらゆるクリエイティブ表現の課題を解決。企画・制作・運用まで一貫して伴走します。
                                映像制作を軸に培ってきた表現力と構成力に、
                                デザインの裏側まで理解し自ら動かせるエンジニアリングの柔軟性を加え、
                                一貫対応でクライアントの想いを形にします。
                            </p>
                            <a href="service.html#creative-division" class="arrow-link purple">
                                <span class="text-sm">詳しく見る</span>
                                <span class="arrow"></span>
                            </a>
                        </div>

                        <!-- 右カラム -->
                        <div class="md:w-2/5">
                            <div class="flex items-center mb-8">
                                <div class="w-16 border-t border-gray-500"></div>
                                <h4 class="text-sm font-semibold text-purple-200 ml-4 whitespace-nowrap">提供メニュー</h4>
                            </div>

                            <div class="flex flex-wrap gap-3">
                                <span
                                    class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                    クリエイティブトータル支援
                                </span>
                                <span
                                    class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                    映像制作
                                </span>
                                <span
                                    class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                    アニメーション制作
                                </span>
                                <span
                                    class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                    Web/アプリ制作
                                </span>
                                <span
                                    class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                    UI設計・LP制作
                                </span>
                                <span
                                    class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                    SNS運用支援
                                </span>
                                <span
                                    class="text-sm text-white bg-gray-500 rounded-full px-3 py-[0.3rem] leading-tight flex items-center">
                                    プロモーション企画
                                </span>
                            </div>

                        </div>

                    </div>
                </div>
            </div>
            <!-- ライン -->


        </div>
    </section>


    <!-- Company Section -->
    <section id="company" class="bg-gray-800 bg-opacity-70 text-white">
        <div class="max-w-6xl mx-auto px-6 pt-24 pb-24 md:px-12 md:pt-36 md:pb-24">
            <div class="flex justify-between items-center mb-16">
                <h2 class="text-4xl md:text-6xl montserrat-bold">COMPANY</h2>
                <a href="company.html" class="flex items-center text-blue-300 hover:text-blue-100 transition">
                    <span class="text-sm md:text-base montserrat-regular">VIEW MORE &gt;</span>
                </a>
            </div>
            <div class="mb-16 md:flex md:items-start md:gap-12">
                <!-- 左側：テキスト -->
                <div class="md:w-1/2 grid grid-rows-[1fr_auto] gap-4">
                    <h3 class="text-xl md:text-2xl mb-4">
                        宇宙を身近に、<br>
                        未来を創造する。
                    </h3>
                    <p class="text-xs md:text-sm max-w-3xl mb-12 font-light text-gray-400 leading-loose">
                        株式会社スペースカウボーイは、2025年に設立された宇宙産業とクリエイティブを融合する企業です。
                        最先端の技術と独創的な発想で、宇宙ビジネスの新たな地平を切り拓きます。
                        私たちのビジョンは、誰もが宇宙の可能性に触れられる世界を実現することです。
                    </p>

                    <div>
                        <a href="company.html" class="hero-cta-button inline-block">
                            <span class="flex items-center justify-center">
                                <span class="text-2xl mr-2">🏢</span>会社概要を見る
                            </span>
                        </a>
                    </div>

                </div>
                <!-- 右側：画像 -->
                <div class="mt-8 md:mt-0 md:w-1/2">
                    <img src="static/img/company_overview.jpg" alt="会社紹介画像"
                        class="w-full h-auto opacity-0 transition-opacity duration-1000" loading="lazy"
                        onload="this.classList.remove('opacity-0'); this.classList.add('opacity-100');" />
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="py-24 bg-gradient-to-r from-blue-900/30 to-purple-900/30">
        <div class="max-w-4xl mx-auto px-6 text-center">
            <h2 class="text-2xl md:text-3xl font-bold mb-8 text-white">お気軽にご相談ください</h2>
            <a href="contact.html" class="concept-cta-button">
                まずはご相談ください
            </a>
        </div>
    </section>


    <!-- Footer -->
    <footer class="text-white text-sm mt-20">
        <div class="max-w-6xl mx-auto px-8 grid grid-cols-1 md:grid-cols-4 gap-8">

            <!-- 左：会社名・住所 -->
            <div>
                <h2 class="text-xl montserrat-bold mb-2">SPACE COWBOY</h2>
                <p class="text-xs mb-1">株式会社スペースカウボーイ</p>
                <p class="text-xs text-gray-400 mb-1">〒810-0001</p>
                <p class="text-xs text-gray-400 mb-1">福岡市中央区天神4-6-28 天神ファーストビル7F</p>
            </div>

            <!-- 中央左：ナビゲーション -->
            <div>
                <ul class="space-y-1">
                    <li><a href="company.html" class="montserrat-bold hover:underline">COMPANY</a></li>
                    <li><a href="service.html" class="montserrat-bold hover:underline">SERVICE</a>
                        <ul class="ml-4 mt-1 space-y-1">
                            <li><a href="service.html#space-division"
                                    class="montserrat-regular text-sm text-gray-400 hover:underline">—
                                    SPACE
                                    DIVISION</a></li>
                            <li><a href="service.html#creative-division"
                                    class="montserrat-regular text-sm text-gray-400 hover:underline">— CREATIVE
                                    DIVISION</a></li>
                        </ul>
                    </li>
                    <li><a href="contact.html" class="montserrat-bold hover:underline">CONTACT</a></li>
                    <li><a href="pegasus-wing-fortune/index.html" class="montserrat-bold hover:underline">FORTUNE GAME</a></li>
                </ul>
            </div>

            <!-- 空のカラム -->
            <div></div>

            <!-- 右：SNS -->
            <div class="text-right">
                <ul class="space-y-2">
                    <li class="flex items-center gap-2 justify-end">
                        <!-- Xアイコン -->
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path
                                d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                        </svg>
                        <a href="https://x.com/cutkey5" class="montserrat-regular hover:underline"
                            target="_blank">X(twitter)</a>
                    </li>
                </ul>
            </div>

        </div>

        <!-- 下部：コピーライト -->
        <div class="max-w-6xl mx-auto px-6 text-xs text-gray-500 py-12">
            <div class="border-t border-gray-600 pt-8">
                <div
                    class="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
                    <div class="flex flex-col md:flex-row gap-4">
                        <a href="privacy.html" class="hover:underline">プライバシーポリシー</a>
                        <a href="terms.html" class="hover:underline">利用規約</a>
                        <a href="tokushoho.html" class="hover:underline">特定商取引法に基づく表記</a>
                    </div>
                    <div>
                        &copy;2025 SPACE COWBOY .Inc
                    </div>
                </div>
            </div>
        </div>

    </footer>

    <script src="static/js/main.js"></script>
    <script>
        // index.html専用の初期化
        SpaceCowboy.init({
            starCount: 100,
            enableSmoothScroll: true
        });
    </script>
</body>

</html>
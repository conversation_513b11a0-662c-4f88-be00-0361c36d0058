body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #0a0a0a;
    overflow-x: hidden;
}

/* CTAボタンスタイル */
.hero-cta-button,
.concept-cta-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 16px 32px;
    border-radius: 50px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    text-transform: none;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.hero-cta-button:hover,
.concept-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.hero-cta-button:active,
.concept-cta-button:active {
    transform: translateY(0);
}

.hero-cta-button::before,
.concept-cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.hero-cta-button:hover::before,
.concept-cta-button:hover::before {
    left: 100%;
}

/* 言語切り替えトグルボタン */
.language-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.toggle-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 6px 12px;
    color: white;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 6px;
}

.toggle-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.toggle-button:active {
    transform: translateY(0);
}

.lang-option {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 2px 4px;
    border-radius: 4px;
}

.lang-option:hover {
    background: rgba(255, 255, 255, 0.1);
}

.lang-option.active {
    font-weight: 700;
    text-decoration: underline;
    text-underline-offset: 3px;
}

.separator {
    color: rgba(255, 255, 255, 0.6);
    font-weight: 300;
}

/* 星空背景 */
.stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: white;
    border-radius: 50%;
    animation: twinkle 3s infinite;
}

@keyframes twinkle {

    0%,
    100% {
        opacity: 0.3;
    }

    50% {
        opacity: 1;
    }
}

/* 人工衛星アニメーション */
.satellite {
    position: fixed;
    width: 30px;
    height: 30px;
    top: 20%;
    animation: orbit 30s linear infinite;
    z-index: -1;
}

@keyframes orbit {
    from {
        transform: translateX(-100px) rotate(0deg);
    }

    to {
        transform: translateX(calc(100vw + 100px)) rotate(360deg);
    }
}


/* メインコンテンツ */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* ロゴ画像のスタイル */

/* スマホ画像のスタイル */
.hero-phone {
    max-width: 90%;
    height: auto;
    margin-top: 30px;
    animation: fadeInUp 1s ease-out 0.5s both;
}

/* モバイル対応 */
@media (max-width: 600px) {
    .hero-logo {
        width: 60px;
    }

    .hero-phone {
        max-width: 100%;
        margin-top: 20px;
    }
}

.pt-serif {
  font-family: 'PT Serif', serif;
}



.hero {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
}

h1 {
    margin-bottom: 20px;
    color: white;
    animation: fadeInUp 1s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.subtitle {
    font-size: clamp(1rem, 3vw, 1.3rem);
    color: #ccc;
    margin-bottom: 40px;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.concept {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    margin: 40px 0;
    color: white;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.concept h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #667eea;
}

.concept p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 15px;
}

/* 登録フォームセクション */
.form-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 40px;
    margin: 40px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out 0.9s both;
}

.form-section h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #333;
}

.progress-bar {
    background: #e0e0e0;
    border-radius: 10px;
    height: 30px;
    position: relative;
    overflow: hidden;
    margin: 20px 0;
}

.progress-fill {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #333;
    font-weight: bold;
    z-index: 1;
}

.milestone {
    display: flex;
    justify-content: space-between;
    margin: 30px 0;
    flex-wrap: wrap;
}

.milestone-item {
    flex: 1;
    text-align: center;
    padding: 10px;
    min-width: 150px;
}

.milestone-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.milestone-label {
    font-size: 0.9rem;
    color: #666;
}

/* Googleフォーム埋め込み */
.form-embed {
    width: 100%;
    height: 600px;
    border: none;
    margin: 20px 0;
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .concept {
        padding: 25px;
    }

    .form-section {
        padding: 25px;
    }

    .milestone {
        flex-direction: column;
    }

    .milestone-item {
        margin-bottom: 20px;
    }
}

/* CTA */
.cta-text {
    font-size: 1.2rem;
    color: #667eea;
    margin: 20px 0;
    font-weight: bold;
}

.note {
    font-size: 0.9rem;
    color: #666;
    margin-top: 20px;
}

/* ボタンスタイル */
.submit-button {
    padding: 12px 30px;
    font-size: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    font-weight: 600;
    text-transform: none;
    position: relative;
    overflow: hidden;
}

.submit-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.submit-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #7c8ef0 0%, #8a5bb8 100%);
}

.submit-button:hover::before {
    left: 100%;
}

.submit-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
    background: linear-gradient(135deg, #5a6dd8 0%, #6d4a94 100%);
}
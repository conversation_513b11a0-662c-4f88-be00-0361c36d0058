project:
  name: "Pegasus Wing Fortune"
  summary: "5枚のペガサスの羽をタップして、小吉/吉/大吉/豚骨/替え玉のいずれかをめくるミニゲーム"
  goal:
    - 名刺やLPで話題のきっかけを作る
    - 1プレイ10秒以内で完結する軽量体験
  target_users:
    - FPの見込み客・来訪者
    - セミナー参加者（スマホ中心）
  platforms:
    - "Mobile Web (iOS/Android, Safari/Chrome)"
    - "Desktop Web (Chrome/Edge/Safari/Firefox)"
  browsers_minimum:
    - iOS: "Safari iOS 15+"
    - Android: "Chrome 105+"
    - Desktop: "Chrome/Edge/Firefox/Safari 最新2メジャー"

gameplay:
  flow:
    - "ロード -> タイトル/説明 -> 5枚の羽が伏せられた状態で表示 -> 1枚タップ -> アニメーションで結果表示 -> もう一度ボタン"
  rules:
    selection: "5枚のうち1枚のみ選択できる（1プレイ1回）"
    reveal_all_after_first: true  # 1枚選択後、残りもめくって結果を見せる（任意）
    replay:
      allow: true
      cooldown_ms: 0
      method: "リセットボタン（もう一度）"
  results:
    types: ["小吉", "吉", "大吉", "豚骨", "替え玉"]
    probabilities:
      # 合計1.0。必要に応じて調整可能
      小吉: 0.36
      吉: 0.32
      大吉: 0.12
      豚骨: 0.10
      替え玉: 0.10
    assignment_strategy:
      type: "shuffle-bag"
      description: "各プレイ開始時点で5枚分の結果プールを確定し、羽へランダム割当してから伏せる。タップで該当カードを公開。"
  scoring: "なし（演出のみ）"
  session_storage:
    enabled: true
    keys:
      best_result: "最高結果（大吉>吉>小吉>替え玉>豚骨の任意順位）"
      plays_count: "累計プレイ数"
    privacy_note: "個人識別情報は保存しない"

ui_ux:
  theme:
    colors:
      background: "#f8fafc"
      primary: "#1f7ae0"       # アクセント（水色系）
      text: "#111827"
      wing_back: "#ffffff"
      wing_border: "#e5e7eb"
      success: "#16a34a"
      neutral: "#6b7280"
    typography:
      base: "system-ui, -apple-system, Segoe UI, Noto Sans JP, sans-serif"
  layout:
    header:
      title: "ペガサスの羽みくじ"
      subtitle: "羽を1枚選んで運勢をチェック！"
    board:
      wings_count: 5
      grid:
        mobile: "grid-cols-2 gap-12 pt-12"
        desktop: "grid-cols-5 gap-16 pt-16"
    cta_buttons:
      primary: "もう一度"
      secondary: "シェア"
  components:
    - name: "WingCard"
      states: ["faceDown", "flipping", "revealed"]
      size:
        mobile: { w: 120, h: 180 }
        desktop: { w: 160, h: 220 }
      visuals:
        front: "羽のピクト（線画）"
        back: "結果テキスト＋小さなアイコン"
      accessibility:
        role: "button"
        aria_label: "羽をめくる"
        keyboard:
          - "Enter/Space で選択"
    - name: "ResultToast"
      behavior: "結果に応じて色とテキストを出す"
      mapping:
        大吉: { color: "success", message: "大吉！最高の一日になりそう" }
        吉: { color: "primary", message: "吉！良い流れに乗れそう" }
        小吉: { color: "neutral", message: "小吉。肩の力を抜いて◎" }
        豚骨: { color: "neutral", message: "豚骨！替え玉いっとく？" }
        替え玉: { color: "primary", message: "替え玉！もう一杯いきますか" }
  animation:
    flip:
      type: "CSS 3D transform"
      duration_ms: 500
      easing: "cubic-bezier(0.22, 1, 0.36, 1)"
    entry_stagger_ms: 60
    toast_fade_ms: 240
  sounds:
    enabled: false  # 既定は無音（会場配慮）。必要なら後付け可

i18n:
  default_lang: "ja"
  strings:
    title: "ペガサスの羽みくじ"
    subtitle: "羽を1枚選んで運勢をチェック！"
    play_again: "もう一度"
    share: "シェア"
    howto: "5枚の羽から1枚をタップしてください"
    a11y_pick: "羽を選択"
    results:
      小吉: "小吉"
      吉: "吉"
      大吉: "大吉"
      豚骨: "豚骨"
      替え玉: "替え玉"

logic:
  random:
    method: "crypto.getRandomValues() を優先、なければ Math.random()"
    seed_support: false
  reveal:
    on_first_pick: "選んだ1枚のみ即時公開"
    reveal_rest: "1秒後に残りを自動でめくる（オプション）"
  replay_reset:
    clear_states: ["WingCard.state", "ResultToast.visible"]
    regenerate_pool: true

state_machine:
  states:
    - IDLE: "初期/リセット直後。全羽は伏せ状態"
    - PICKED: "プレイヤーが1枚選択、結果を表示"
    - REVEAL_ALL: "残りの羽を自動で表示（任意）"
    - COMPLETE: "トースト表示、再プレイ待機"
  transitions:
    - from: IDLE
      to: PICKED
      on: "WingCard.click"
    - from: PICKED
      to: REVEAL_ALL
      after_ms: 1000
    - from: REVEAL_ALL
      to: COMPLETE
      after_ms: 500
    - from: COMPLETE
      to: IDLE
      on: "PlayAgain.click"

files:
  structure:
    - index.html
    - /assets/icons/wing.svg
    - /assets/icons/fortune-ramen.svg
    - /assets/icons/fortune-star.svg
    - /styles/main.css
    - /scripts/app.js
  index_html:
    sections:
      - header: "タイトル・説明"
      - board: "羽5枚（button要素）"
      - controls: "もう一度/シェア"
      - footer: "クレジット・利用案内"

css:
  principles:
    - "モバイルファースト"
    - "CSS変数でテーマ管理"
    - "prefers-reduced-motion 対応でアニメ減"
  key_classes:
    - ".board { display:grid }"
    - ".wing { perspective: 1000px }"
    - ".wing__inner { transform-style: preserve-3d; transition: transform .5s }"
    - ".wing--revealed .wing__inner { transform: rotateY(180deg) }"
    - ".card-front, .card-back { backface-visibility:hidden }"
    - ".card-back { transform: rotateY(180deg) }"
  responsiveness:
    mobile_breakpoint_px: 768

javascript:
  modules:
    - name: "rng"
      exports: ["choiceWeighted", "shuffle"]
    - name: "gameState"
      exports: ["init", "reset", "assignPool", "reveal", "revealAll"]
    - name: "ui"
      exports: ["renderBoard", "bindEvents", "showToast", "updateStats"]
  events:
    - "click .wing"
    - "keydown Enter/Space on .wing"
    - "click #playAgain"
    - "click #share"
  pseudocode:
    - |
      onLoad:
        state = init()
        renderBoard(5)
        assignPool(probabilities)  # 5件の結果を生成して各羽に割当
        bindEvents()

      onWingClick(wing):
        if state.phase != IDLE: return
        reveal(wing)
        showToast(resultOf(wing))
        setTimeout(revealAll, 1000)
        state.phase = COMPLETE

      onPlayAgain:
        reset()
        assignPool(probabilities)
        renderBoard(5)

  web_share_api:
    use: true
    fallback: "navigator.clipboard.writeText(url)"

accessibility:
  aria:
    - "各羽に aria-pressed=false/true を付与"
    - "結果には role=alert を付与し読み上げ"
  keyboard:
    - "Tabで羽フォーカス、Enter/Spaceで選択"
  reduced_motion:
    respect: true

telemetry:  # 任意
  enabled: false
  events:
    - "play_start"
    - "wing_pick"
    - "result_shown"
    - "replay"

performance:
  targets:
    first_interaction_ready_ms: 800
    js_bundle_kb_gzip: 20
  techniques:
    - "SVGアイコンのインライン化"
    - "CSS/JS最小限・遅延読込なし（即時可用）"

security_privacy:
  tracking: "なし（クッキー不使用）"
  storage: "localStorage に最小限のカウントのみ（任意）"
  external_calls: "なし"

branding:
  option:
    wing_style: "ラインアイコン（モノクロ）"
    accent_mizuiro_hex: "#46A3FF"
    font: "Noto Sans JP"
  footer_credit: "© Space Cowboy Inc."

acceptance_criteria:
  - "5枚の羽が表示され、どれか1枚をタップすると結果が0.5秒以内に表示される"
  - "結果は {小吉, 吉, 大吉, 豚骨, 替え玉} のいずれか"
  - "もう一度ボタンで即リセットし、新しい割当で再プレイ可能"
  - "スマホ縦画面でレイアウト崩れがない（iPhone 12, Pixel 6で確認）"
  - "アクセシビリティ：キーボード操作可能、ARIAが有効"
  - "通信不要・オフラインでも動作"
  - "アニメ軽量で60fps相当（reduced-motion時は無効化）"

testing:
  cases:
    - name: "単一選択"
      steps: ["羽#1をクリック"]
      expect: ["#1がrevealed", "他は伏せたまま→1秒後オプションで公開"]
    - name: "連打耐性"
      steps: ["同一羽を高速連打"]
      expect: ["2度目以降の入力は無視されフリーズしない"]
    - name: "キーボード操作"
      steps: ["Tabで羽にフォーカス→Enter"]
      expect: ["結果表示"]
    - name: "確率検証（開発時）"
      steps: ["1000回シミュレーション"]
      expect: ["分布が±3%以内に収束"]

future_extensions:
  - "結果に応じたミニTips表示（資産運用や福岡グルメ豆知識）"
  - "結果シェア用OG画像の動的生成"
  - "多言語対応（en/zh）"

<!DOCTYPE html>
<html lang="ja">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Card Position Test</title>
    <style>
        body {
            background: #1a1a2e;
            padding: 50px;
            font-family: Arial, sans-serif;
        }

        .test-container {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
            margin-bottom: 30px;
        }

        .card {
            width: 120px;
            height: 180px;
            background: linear-gradient(135deg, #2ea7e0 0%, #00a8b5 100%);
            border-radius: 16px;
            border: 2px solid #00a8b5;
            cursor: pointer;
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        /* Test 1: Basic transform */
        .test1 .card {
            transform: translateY(10px);
        }
        .test1 .card.selected {
            transform: translateY(0px) !important;
        }

        /* Test 2: Animation-free approach */
        .test2 .card {
            margin-top: 10px;
        }
        .test2 .card.selected {
            margin-top: 0px;
        }

        /* Test 3: Position relative approach */
        .test3 .card {
            position: relative;
            top: 10px;
        }
        .test3 .card.selected {
            top: 0px;
        }

        .test-title {
            color: white;
            margin: 20px 0 10px 0;
            font-size: 18px;
        }

        .log {
            background: #333;
            color: #fff;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 200px;
            overflow-y: auto;
        }

        .controls {
            margin: 20px 0;
        }

        button {
            background: #2ea7e0;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <h1 style="color: white;">Card Position Test</h1>
    
    <div class="controls">
        <button onclick="resetAllTests()">Reset All</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="log" id="log">Log will appear here...</div>

    <div class="test-title">Test 1: Transform approach (current method)</div>
    <div class="test-container test1">
        <div class="card" data-test="1" data-index="0">Card 0</div>
        <div class="card" data-test="1" data-index="1">Card 1</div>
        <div class="card" data-test="1" data-index="2">Card 2</div>
        <div class="card" data-test="1" data-index="3">Card 3</div>
        <div class="card" data-test="1" data-index="4">Card 4</div>
    </div>

    <div class="test-title">Test 2: Margin approach</div>
    <div class="test-container test2">
        <div class="card" data-test="2" data-index="0">Card 0</div>
        <div class="card" data-test="2" data-index="1">Card 1</div>
        <div class="card" data-test="2" data-index="2">Card 2</div>
        <div class="card" data-test="2" data-index="3">Card 3</div>
        <div class="card" data-test="2" data-index="4">Card 4</div>
    </div>

    <div class="test-title">Test 3: Position relative approach</div>
    <div class="test-container test3">
        <div class="card" data-test="3" data-index="0">Card 0</div>
        <div class="card" data-test="3" data-index="1">Card 1</div>
        <div class="card" data-test="3" data-index="2">Card 2</div>
        <div class="card" data-test="3" data-index="3">Card 3</div>
        <div class="card" data-test="3" data-index="4">Card 4</div>
    </div>

    <script>
        function logMessage(message) {
            const log = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `[${timestamp}] ${message}<br>`;
            log.scrollTop = log.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function resetAllTests() {
            document.querySelectorAll('.card.selected').forEach(card => {
                card.classList.remove('selected');
            });
            logMessage('All tests reset');
        }

        function measurePosition(card, testNum, cardIndex, phase) {
            const rect = card.getBoundingClientRect();
            const style = window.getComputedStyle(card);
            const transform = style.transform;
            const marginTop = style.marginTop;
            const top = style.top;
            
            logMessage(`Test ${testNum} Card ${cardIndex} ${phase}: top=${rect.top.toFixed(2)}px, transform=${transform}, margin-top=${marginTop}, top=${top}`);
        }

        // Add click handlers
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', function() {
                const testNum = this.dataset.test;
                const cardIndex = this.dataset.index;
                const testContainer = this.closest('.test-container');
                
                // Reset all cards in this test
                testContainer.querySelectorAll('.card').forEach(c => {
                    c.classList.remove('selected');
                });
                
                // Measure before
                measurePosition(this, testNum, cardIndex, 'BEFORE');
                
                // Add selected class
                this.classList.add('selected');
                
                // Measure after (with slight delay to account for transitions)
                setTimeout(() => {
                    measurePosition(this, testNum, cardIndex, 'AFTER');
                    
                    // Measure all other cards for comparison
                    testContainer.querySelectorAll('.card').forEach((otherCard, index) => {
                        if (otherCard !== this) {
                            const otherRect = otherCard.getBoundingClientRect();
                            const otherStyle = window.getComputedStyle(otherCard);
                            logMessage(`  Other Card ${index}: top=${otherRect.top.toFixed(2)}px, transform=${otherStyle.transform}`);
                        }
                    });
                    logMessage('---');
                }, 50);
            });
        });

        logMessage('Card Position Test Ready. Click cards to test positioning.');
    </script>
</body>
</html>
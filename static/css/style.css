body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON>o, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #0a0a0a;
    overflow-x: hidden;
}

/* モバイルメニューのアニメーション */
@keyframes slideDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        transform: translateY(0);
        opacity: 1;
    }
    to {
        transform: translateY(-100%);
        opacity: 0;
    }
}

#mobile-menu {
    overflow: hidden;
}

#mobile-menu.hidden {
    display: none;
}

#mobile-menu.slide-down {
    display: block !important;
    animation: slideDown 0.3s ease-out forwards;
}

#mobile-menu.slide-up {
    display: block !important;
    animation: slideUp 0.3s ease-out forwards;
}




.hero-content {
  position: relative;
  padding: 120px 24px;
  text-align: center;
  color: white;
  z-index: 2;
}



/* CTAボタンスタイル */
.hero-cta-button,
.concept-cta-button {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 16px 32px;
    border-radius: 50px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    text-transform: none;
    letter-spacing: 0.5px;
    position: relative;
    overflow: hidden;
}

.hero-cta-button:hover,
.concept-cta-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.6);
    background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
}

.hero-cta-button:active,
.concept-cta-button:active {
    transform: translateY(0);
}

.hero-cta-button::before,
.concept-cta-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.hero-cta-button:hover::before,
.concept-cta-button:hover::before {
    left: 100%;
}

/* 言語切り替えトグルボタン */
.language-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
}

.toggle-button {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 6px 12px;
    color: white;
    font-size: 12px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    gap: 6px;
}

.toggle-button:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.toggle-button:active {
    transform: translateY(0);
}

.lang-option {
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 2px 4px;
    border-radius: 4px;
}

.lang-option:hover {
    background: rgba(255, 255, 255, 0.1);
}

.lang-option.active {
    font-weight: 700;
    text-decoration: underline;
    text-underline-offset: 3px;
}

.separator {
    color: rgba(255, 255, 255, 0.6);
    font-weight: 300;
}

/* 星空背景 */
.stars {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
}

.star {
    position: absolute;
    width: 2px;
    height: 2px;
    background: white;
    border-radius: 50%;
    animation: twinkle 3s infinite;
}

@keyframes twinkle {

    0%,
    100% {
        opacity: 0.3;
    }

    50% {
        opacity: 1;
    }
}

/* 人工衛星アニメーション */
.satellite {
    position: fixed;
    width: 30px;
    height: 30px;
    top: 20%;
    animation: orbit 30s linear infinite;
    z-index: -1;
}

@keyframes orbit {
    from {
        transform: translateX(-100px) rotate(0deg);
    }

    to {
        transform: translateX(calc(100vw + 100px)) rotate(360deg);
    }
}


/* メインコンテンツ */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
    z-index: 1;
}

/* ロゴ画像のスタイル */

/* スマホ画像のスタイル */
.hero-phone {
    max-width: 90%;
    height: auto;
    margin-top: 30px;
    animation: fadeInUp 1s ease-out 0.5s both;
}

/* モバイル対応 */
@media (max-width: 600px) {
    .hero-logo {
        width: 60px;
    }

    .hero-phone {
        max-width: 100%;
        margin-top: 20px;
    }
}

.pt-serif {
  font-family: 'PT Serif', serif;
}



.hero {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    color: white;
}

h1 {
    margin-bottom: 20px;
    color: white;
    animation: fadeInUp 1s ease-out;
}

/* ヒーロータイトル専用 */
#hero-title {
    animation: fadeInUpSlow 2s ease-out;
}

/* ナビゲーション */
nav {
    animation: fadeIn 2s ease-out 1s both;
}

/* ヒーローサブタイトル */
#hero-subtitle-1,
#hero-subtitle-2 {
    animation: fadeIn 2s ease-out 1.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* ゆっくりしたタイトルアニメーション */
@keyframes fadeInUpSlow {
    from {
        opacity: 0;
        transform: translateY(40px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* フェードインアニメーション */
@keyframes fadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

/* 背景画像ズームアニメーション */
@keyframes zoomOut {
    from {
        transform: scale(1.05);
    }

    to {
        transform: scale(1);
    }
}

.subtitle {
    font-size: clamp(1rem, 3vw, 1.3rem);
    color: #ccc;
    margin-bottom: 40px;
    animation: fadeInUp 1s ease-out 0.3s both;
}

.concept {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 40px;
    margin: 40px 0;
    color: white;
    animation: fadeInUp 1s ease-out 0.6s both;
}

.concept h2 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #667eea;
}

.concept p {
    font-size: 1.1rem;
    line-height: 1.8;
    margin-bottom: 15px;
}

/* 登録フォームセクション */
.form-section {
    background: rgba(255, 255, 255, 0.8);
    border-radius: 20px;
    padding: 40px;
    margin: 40px 0;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out 0.9s both;
}

.form-section h3 {
    font-size: 1.8rem;
    margin-bottom: 20px;
    color: #333;
}

.progress-bar {
    background: #e0e0e0;
    border-radius: 10px;
    height: 30px;
    position: relative;
    overflow: hidden;
    margin: 20px 0;
}

.progress-fill {
    background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
    position: relative;
}

.progress-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #333;
    font-weight: bold;
    z-index: 1;
}

.milestone {
    display: flex;
    justify-content: space-between;
    margin: 30px 0;
    flex-wrap: wrap;
}

.milestone-item {
    flex: 1;
    text-align: center;
    padding: 10px;
    min-width: 150px;
}

.milestone-number {
    font-size: 2rem;
    font-weight: bold;
    color: #667eea;
}

.milestone-label {
    font-size: 0.9rem;
    color: #666;
}

/* Googleフォーム埋め込み */
.form-embed {
    width: 100%;
    height: 600px;
    border: none;
    margin: 20px 0;
}

/* レスポンシブ対応 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    .concept {
        padding: 25px;
    }

    .form-section {
        padding: 25px;
    }

    .milestone {
        flex-direction: column;
    }

    .milestone-item {
        margin-bottom: 20px;
    }
}

/* CTA */
.cta-text {
    font-size: 1.2rem;
    color: #667eea;
    margin: 20px 0;
    font-weight: bold;
}

.note {
    font-size: 0.9rem;
    color: #666;
    margin-top: 20px;
}

/* ボタンスタイル */
.submit-button {
    padding: 12px 30px;
    font-size: 1rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
    font-weight: 600;
    text-transform: none;
    position: relative;
    overflow: hidden;
}

.submit-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.submit-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #7c8ef0 0%, #8a5bb8 100%);
}

.submit-button:hover::before {
    left: 100%;
}

.submit-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(102, 126, 234, 0.3);
    background: linear-gradient(135deg, #5a6dd8 0%, #6d4a94 100%);
}

/* パララックス背景 */
.parallax-bg {
    background-image: url('../img/universe.jpg');
    background-size: cover;
    background-position: center;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100vh;
    z-index: -1;
    will-change: transform;
    animation: fadeIn 0.5s ease-in, zoomOut 5s ease-out;
}

/* サービスページ専用背景 */
.service-page .parallax-bg {
    background-image: url('../img/service.jpg');
}

/* 会社概要ページ専用背景 */
.company-page .parallax-bg {
    background-image: url('../img/company_image.jpg');
}

/* お問い合わせページ専用背景 */
.contact-page .parallax-bg {
    background-image: url('../img/contact_image.jpg');
}

.parallax-bg::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.4);
    z-index: 1;
    pointer-events: none;
}


/* デスクトップのみbackground-attachment: fixedを使用 */
@media (min-width: 1025px) {
    .parallax-bg {
        background-attachment: fixed;
        transform: none !important;
    }
}

/* iOS/モバイル対応 */
@supports (-webkit-touch-callout: none) {
    .parallax-bg {
        background-attachment: scroll;
        height: 120vh; /* スクロール時の余白対策 */
    }
}

/* 追加のiOS対策 */
@media only screen and (max-width: 1024px) {
    .parallax-bg {
        background-attachment: scroll;
        position: fixed;
        height: 120vh;
        transform: translateZ(0);
        -webkit-transform: translateZ(0);
        -webkit-backface-visibility: hidden;
        backface-visibility: hidden;
    }
}

.parallax-wrapper {
    position: relative;
    z-index: 1;
    background-color: transparent;
    min-height: 100vh;
}

/* 矢印リンク */
.arrow-link {
    display: inline-flex;
    align-items: center;
    color: #60a5fa; /* blue-400 */
    text-decoration: none;
    transition: all 0.3s ease;
    margin-top: 1rem;
}

.arrow-link:hover {
    color: #93c5fd; /* blue-300 */
}

.arrow-link:hover .arrow {
    transform: translateX(5px);
}

.arrow {
    position: relative;
    width: 30px;
    height: 1px;
    background-color: currentColor;
    margin-left: 0.5rem;
    transition: transform 0.3s ease;
}

.arrow::after {
    content: '';
    position: absolute;
    top: 1px;
    right: 0;
    transform-origin: right bottom;
    width: 10px;
    height: 1px;
    background-color: currentColor;
    transform: rotate(45deg);
}

/* パープル系の矢印リンク */
.arrow-link.purple {
    color: #c084fc; /* purple-400 */
}

.arrow-link.purple:hover {
    color: #d8b4fe; /* purple-300 */
}

<!DOCTYPE html>
<html lang="ja">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>お問い合わせ | 株式会社スペースカウボーイ</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- ファビコン -->
    <link rel="icon" type="image/x-icon" href="static/img/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="static/img/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="static/img/icon-32x32.png">
    <link rel="apple-touch-icon" sizes="180x180" href="static/img/apple-touch-icon-180x180.png">
    
    <!-- OGP設定（検索結果・SNSシェア用） -->
    <meta property="og:title" content="お問い合わせ | 株式会社スペースカウボーイ">
    <meta property="og:description" content="宇宙事業・映像制作・Web開発に関するご相談・お問い合わせはこちらから。無料ヒヤリングも承っております。">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.space-cowboy.jp/contact.html?v=2">
    <meta property="og:image" content="https://www.space-cowboy.jp/static/img/ogp1200x630.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:type" content="image/png">
    <meta property="og:site_name" content="株式会社スペースカウボーイ">
    <meta property="og:locale" content="ja_JP">
    
    <!-- Twitter Card設定 -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@cutkey5">
    
    <style>
        body {
            font-family: 'Noto Sans JP', sans-serif;
        }

        .montserrat-regular {
            font-family: "Montserrat", sans-serif;
            font-weight: 420;
            font-style: normal;
        }

        .montserrat-bold {
            font-family: "Montserrat", sans-serif;
            font-weight: 560;
            font-style: normal;
        }
    </style>
</head>

<body class="bg-gray-900 text-white contact-page">
    <!-- 星空背景 -->
    <div class="stars" id="stars"></div>

    <!-- Navigation Section -->
    <nav class="fixed top-0 w-full z-50">
        <div class="max-w-7xl mx-auto px-6 py-6 md:px-12 py-12">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2 text-lg text-white montserrat-regular tracking-wide">
                    <a href="index.html">SPACE COWBOY</a>
                </div>

                <div class="md:hidden">
                    <button id="menu-button" class="focus:outline-none text-white p-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16m-7 6h7"></path>
                        </svg>
                    </button>
                </div>
                <div class="hidden md:flex space-x-10">
                    <a href="company.html"
                        class="text-sm font-light text-white hover:text-blue-400 transition-colors montserrat-regular">COMPANY</a>
                    <a href="service.html"
                        class="text-sm font-light text-white hover:text-blue-400 transition-colors montserrat-regular">SERVICE</a>
                    <a href="contact.html"
                        class="text-sm font-light text-white hover:text-blue-400 transition-colors montserrat-regular">CONTACT</a>
                </div>
                <!-- Mobile menu -->
                <div id="mobile-menu"
                    class="hidden md:hidden absolute top-full left-0 w-full bg-black/85 backdrop-blur-md border-t border-gray-700/50 shadow-2xl z-50">
                    <div class="flex flex-col px-6 py-8">
                        <a href="company.html"
                            class="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                            <span>COMPANY</span>
                            <svg class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </a>
                        <a href="service.html"
                            class="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                            <span>SERVICE</span>
                            <svg class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </a>
                        <a href="contact.html"
                            class="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular">
                            <span>CONTACT</span>
                            <svg class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- パララックス背景 -->
    <div class="parallax-bg" id="parallax-bg"></div>

    <!-- メインコンテンツ -->
    <div class="parallax-wrapper">
        <section class="min-h-screen pt-32 pb-16 md:pt-48 md:pb-24 relative z-10">
            <div class="max-w-6xl mx-auto px-6 md:px-12">
                <!-- ページタイトル -->
                <div class="mb-24 animate-fadeInUpSlow">
                    <h1 class="text-5xl md:text-7xl montserrat-bold text-white mb-8">CONTACT</h1>
                    <p class="text-xl md:text-2xl text-blue-300 font-light mb-8">
                        お気軽にお問い合わせください
                    </p>
                    <p class="text-base md:text-lg text-gray-300 leading-loose max-w-4xl">
                        あなたのアイデアや質問をお聞かせください。私たちと一緒に宇宙と表現の可能性を広げていきましょう。
                    </p>
                </div>
            </div>

            <!-- お問い合わせフォーム -->
            <div class="w-full bg-gray-900 bg-opacity-80">
                <div class="max-w-4xl mx-auto px-6 md:px-12 py-24">
                    <form id="contactForm" class="max-w-2xl mx-auto space-y-8">
                        <div class="grid md:grid-cols-2 gap-8">
                            <!-- お名前 -->
                            <div>
                                <label for="name" class="block text-sm font-medium mb-3 text-gray-300">お名前 *</label>
                                <input type="text" id="name" name="name" required
                                    class="w-full px-0 py-4 bg-transparent text-white border-0 border-b-2 border-gray-600 focus:border-blue-400 outline-none transition-colors placeholder-gray-500">
                            </div>

                            <!-- 会社名・団体名 -->
                            <div>
                                <label for="company"
                                    class="block text-sm font-medium mb-3 text-gray-300">会社名・団体名</label>
                                <input type="text" id="company" name="company"
                                    class="w-full px-0 py-4 bg-transparent text-white border-0 border-b-2 border-gray-600 focus:border-blue-400 outline-none transition-colors placeholder-gray-500">
                            </div>
                        </div>

                        <!-- メールアドレス -->
                        <div>
                            <label for="email" class="block text-sm font-medium mb-3 text-gray-300">メールアドレス *</label>
                            <input type="email" id="email" name="email" required
                                class="w-full px-0 py-4 bg-transparent text-white border-0 border-b-2 border-gray-600 focus:border-blue-400 outline-none transition-colors placeholder-gray-500">
                        </div>

                        <!-- 電話番号 -->
                        <div>
                            <label for="phone" class="block text-sm font-medium mb-3 text-gray-300">電話番号</label>
                            <input type="tel" id="phone" name="phone"
                                class="w-full px-0 py-4 bg-transparent text-white border-0 border-b-2 border-gray-600 focus:border-blue-400 outline-none transition-colors placeholder-gray-500">
                        </div>



                        <!-- メッセージ -->
                        <div>
                            <label for="message" class="block text-sm font-medium mb-3 text-gray-300">お問合せ内容 *</label>
                            <textarea id="message" name="message" rows="6" required
                                class="w-full px-0 py-4 bg-transparent text-white border-0 border-b-2 border-gray-600 focus:border-blue-400 outline-none transition-colors resize-vertical placeholder-gray-500"
                                placeholder="お問い合わせ内容を詳しくお聞かせください。"></textarea>
                        </div>

                        <!-- ハニーポット（スパム対策） -->
                        <div style="position: absolute; left: -5000px;" aria-hidden="true">
                            <input type="text" id="website" name="website" tabindex="-1" autocomplete="off">
                        </div>

                        <!-- プライバシーポリシー同意 -->
                        <div class="flex items-start space-x-3">
                            <input type="checkbox" id="privacy_policy" name="privacy_policy" required
                                class="mt-1 w-4 h-4 text-blue-600 bg-transparent border-2 border-gray-600 focus:border-blue-400 focus:ring-0 outline-none">
                            <label for="privacy_policy" class="text-sm text-gray-300 leading-relaxed">
                                <a href="privacy.html" target="_blank"
                                    class="text-blue-400 hover:text-blue-300 underline">プライバシーポリシー</a>に同意します *
                            </label>
                        </div>

                        <!-- 送信ボタン -->
                        <div class="text-center pt-8">
                            <button type="submit" id="submitButton" class="hero-cta-button inline-flex items-center">
                                <span class="text-2xl mr-2">📧</span>
                                <span id="buttonText">送信する</span>
                            </button>
                            <div id="formMessage" class="mt-4 text-sm"></div>
                        </div>
                    </form>
                </div>
            </div>
        </section>
    </div>

    <!-- Footer -->
    <footer class="text-white text-sm mt-20">
        <div class="max-w-6xl mx-auto px-8 grid grid-cols-1 md:grid-cols-4 gap-8">

            <!-- 左：会社名・住所 -->
            <div>
                <h2 class="text-xl montserrat-bold mb-2">SPACE COWBOY</h2>
                <p class="text-xs mb-1">株式会社スペースカウボーイ</p>
                <p class="text-xs text-gray-400 mb-1">〒810-0001</p>
                <p class="text-xs text-gray-400 mb-1">福岡市中央区天神4-6-28 天神ファーストビル7F</p>
            </div>

            <!-- 中央左：ナビゲーション -->
            <div>
                <ul class="space-y-1">
                    <li><a href="company.html" class="montserrat-bold hover:underline">COMPANY</a></li>
                    <li><a href="service.html" class="montserrat-bold hover:underline">SERVICE</a>
                        <ul class="ml-4 mt-1 space-y-1">
                            <li><a href="service.html#space-division"
                                    class="montserrat-regular text-sm text-gray-400 hover:underline">—
                                    SPACE
                                    DIVISION</a></li>
                            <li><a href="service.html#creative-division"
                                    class="montserrat-regular text-sm text-gray-400 hover:underline">— CREATIVE
                                    DIVISION</a></li>
                        </ul>
                    </li>
                    <li><a href="contact.html" class="montserrat-bold hover:underline">CONTACT</a></li>
                </ul>
            </div>

            <!-- 空のカラム -->
            <div></div>

            <!-- 右：SNS -->
            <div class="text-right">
                <ul class="space-y-2">
                    <li class="flex items-center gap-2 justify-end">
                        <!-- Xアイコン -->
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path
                                d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                        </svg>
                        <a href="https://x.com/cutkey5" class="montserrat-regular hover:underline"
                            target="_blank">X(twitter)</a>
                    </li>
                </ul>
            </div>

        </div>

        <!-- 下部：コピーライト -->
        <div class="max-w-6xl mx-auto px-6 text-xs text-gray-500 py-12">
            <div class="border-t border-gray-600 pt-8">
                <div
                    class="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
                    <div class="flex flex-col md:flex-row gap-4">
                        <a href="privacy.html" class="hover:underline">プライバシーポリシー</a>
                        <a href="terms.html" class="hover:underline">利用規約</a>
                        <a href="tokushoho.html" class="hover:underline">特定商取引法に基づく表記</a>
                    </div>
                    <div>
                        &copy;2025 SPACE COWBOY .Inc
                    </div>
                </div>
            </div>
        </div>

    </footer>

    <script src="static/js/main.js"></script>
    <script>
        // contact.html専用の初期化
        SpaceCowboy.init({
            starCount: 100,
            enableSmoothScroll: false
        });

        // フォーム読み込み時刻を記録（スパム対策）
        const formLoadTime = Date.now();

        // フォーム送信処理
        document.getElementById('contactForm').addEventListener('submit', function (e) {
            e.preventDefault();

            const submitButton = document.getElementById('submitButton');
            const buttonText = document.getElementById('buttonText');
            const formMessage = document.getElementById('formMessage');

            // ハニーポットチェック（スパム対策）
            if (document.getElementById('website').value) {
                // スパムボットの可能性が高い
                formMessage.innerHTML = '<p class="text-red-400">エラーが発生しました。</p>';
                return;
            }

            // 時間チェック（3秒以内の送信を拒否）
            const submitTime = Date.now();
            if (submitTime - formLoadTime < 3000) {
                formMessage.innerHTML = '<p class="text-red-400">送信が早すぎます。もう一度お試しください。</p>';
                return;
            }

            // ボタンを無効化し、送信中の表示に変更
            submitButton.disabled = true;
            buttonText.textContent = '送信中...';

            // フォームデータを取得
            const formData = {
                name: document.getElementById('name').value,
                company: document.getElementById('company').value,
                email: document.getElementById('email').value,
                phone: document.getElementById('phone').value,
                message: document.getElementById('message').value,
                timestamp: new Date().toLocaleString('ja-JP', { timeZone: 'Asia/Tokyo' })
            };

            // GASのWebアプリURLを設定（後で実際のURLに置き換えてください）
            const scriptURL = 'https://script.google.com/macros/s/AKfycbyqu5PtlZtuzkp9ETIISQEbbsqCJN4rhvKJtQfFTPYYaEkvAoGPpfnLpj7VP98FfE0j/exec';

            // データを送信
            fetch(scriptURL, {
                method: 'POST',
                mode: 'no-cors',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(formData)
            })
                .then(() => {
                    // 成功時の処理
                    formMessage.innerHTML = '<p class="text-green-400">お問い合わせありがとうございます。<br>内容を確認次第、ご連絡させていただきます。</p>';
                    formMessage.classList.add('animate-fadeIn');
                    document.getElementById('contactForm').reset();
                    buttonText.textContent = '送信完了';

                    // 3秒後にボタンを元に戻す
                    setTimeout(() => {
                        submitButton.disabled = false;
                        buttonText.textContent = '送信する';
                    }, 3000);
                })
                .catch(error => {
                    // エラー時の処理
                    console.error('Error:', error);
                    formMessage.innerHTML = '<p class="text-red-400">送信中にエラーが発生しました。<br>しばらくしてから再度お試しください。</p>';
                    submitButton.disabled = false;
                    buttonText.textContent = '送信する';
                });
        });
    </script>
</body>

</html>
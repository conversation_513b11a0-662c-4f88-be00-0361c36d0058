<!DOCTYPE html>
<html lang="ja">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会社概要 | 株式会社スペースカウボーイ</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="static/css/style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@400;500;700&display=swap" rel="stylesheet">
    <!-- ファビコン -->
    <link rel="icon" type="image/x-icon" href="static/img/favicon.ico">
    <link rel="icon" type="image/png" sizes="32x32" href="static/img/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="static/img/icon-32x32.png">
    <link rel="apple-touch-icon" sizes="180x180" href="static/img/apple-touch-icon-180x180.png">
    
    <!-- OGP設定（検索結果・SNSシェア用） -->
    <meta property="og:title" content="会社概要 | 株式会社スペースカウボーイ">
    <meta property="og:description" content="福岡を拠点に宇宙から地上まで表現と技術で課題解決を行う株式会社スペースカウボーイの会社概要・沿革・アクセス情報です。">
    <meta property="og:type" content="website">
    <meta property="og:url" content="https://www.space-cowboy.jp/company.html?v=2">
    <meta property="og:image" content="https://www.space-cowboy.jp/static/img/ogp1200x630.png">
    <meta property="og:image:width" content="1200">
    <meta property="og:image:height" content="630">
    <meta property="og:image:type" content="image/png">
    <meta property="og:site_name" content="株式会社スペースカウボーイ">
    <meta property="og:locale" content="ja_JP">
    
    <!-- Twitter Card設定 -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:site" content="@cutkey5">
    
    <style>
        body {
            font-family: 'Noto Sans JP', sans-serif;
        }

        .montserrat-regular {
            font-family: "Montserrat", sans-serif;
            font-weight: 420;
            font-style: normal;
        }

        .montserrat-bold {
            font-family: "Montserrat", sans-serif;
            font-weight: 560;
            font-style: normal;
        }
    </style>
</head>

<body class="bg-gray-900 text-white company-page">
    <!-- 星空背景 -->
    <div class="stars" id="stars"></div>

    <!-- Navigation Section -->
    <nav class="fixed top-0 w-full z-50">
        <div class="max-w-7xl mx-auto px-6 py-6 md:px-12 py-12">
            <div class="flex justify-between items-center">
                <div class="flex items-center space-x-2 text-lg text-white montserrat-regular tracking-wide">
                    <a href="index.html">SPACE COWBOY</a>
                </div>

                <div class="md:hidden">
                    <button id="menu-button" class="focus:outline-none text-white p-2">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                d="M4 6h16M4 12h16m-7 6h7"></path>
                        </svg>
                    </button>
                </div>
                <div class="hidden md:flex space-x-10">
                    <a href="company.html"
                        class="text-sm font-light text-white hover:text-blue-400 transition-colors montserrat-regular">COMPANY</a>
                    <a href="service.html"
                        class="text-sm font-light text-white hover:text-blue-400 transition-colors montserrat-regular">SERVICE</a>
                    <a href="contact.html"
                        class="text-sm font-light text-white hover:text-blue-400 transition-colors montserrat-regular">CONTACT</a>
                </div>
                <!-- Mobile menu -->
                <div id="mobile-menu"
                    class="hidden md:hidden absolute top-full left-0 w-full bg-black/85 backdrop-blur-md border-t border-gray-700/50 shadow-2xl z-50">
                    <div class="flex flex-col px-6 py-8">
                        <a href="company.html"
                            class="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                            <span>COMPANY</span>
                            <svg class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </a>
                        <a href="service.html"
                            class="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular border-b border-gray-700/30 hover:border-blue-400/50">
                            <span>SERVICE</span>
                            <svg class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </a>
                        <a href="contact.html"
                            class="group flex items-center justify-between py-4 text-base font-light text-white hover:text-blue-400 transition-all duration-300 montserrat-regular">
                            <span>CONTACT</span>
                            <svg class="w-4 h-4 text-gray-500 group-hover:text-blue-400 transition-colors duration-300"
                                fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7">
                                </path>
                            </svg>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- パララックス背景 -->
    <div class="parallax-bg" id="parallax-bg"></div>

    <!-- メインコンテンツ -->
    <div class="parallax-wrapper">
        <section class="min-h-screen pt-32 pb-16 md:pt-48 md:pb-24 relative z-10">
            <div class="max-w-6xl mx-auto px-6 md:px-12">
                <!-- ページタイトル -->
                <div class="mb-56 animate-fadeInUpSlow">
                    <h1 class="text-5xl md:text-7xl montserrat-bold text-white mb-8">COMPANY</h1>
                    <p class="text-xl md:text-2xl text-blue-300 font-light mb-8">
                        宇宙とクリエイティブの交差点で、新しい価値を
                    </p>
                </div>
            </div>

            <!-- 会社情報エリア -->
            <div class="w-full bg-gray-900 bg-opacity-80">
                <div class="max-w-6xl mx-auto px-6 md:px-12 py-24 space-y-32">


                    <!-- ビジョン -->
                    <div id="vision" class="scroll-mt-32">
                        <div class="border-l-4 border-gray-300 pl-4 mb-12">
                            <p class="text-sm text-gray-300 tracking-widest mb-1">01 — ビジョン</p>
                            <h3 class="text-3xl md:text-4xl montserrat-bold tracking-wide">VISION</h3>
                        </div>
                        <div>
                            <h4 class="text-3xl font-bold text-white mb-3">宇宙を身近に、クリエイティブを自由に。</h4>
                            <p class="text-gray-300 leading-loose">
                                私たちは、宇宙産業とクリエイティブの架け橋となり、<br>
                                誰もが宇宙の可能性に触れられる世界を創造します。
                            </p>
                        </div>
                    </div>

                    <!-- ミッション -->
                    <div id="mission" class="scroll-mt-32">
                        <div class="border-l-4 border-gray-300 pl-4 mb-12">
                            <p class="text-sm text-gray-300 tracking-widest mb-1">02 — ミッション</p>
                            <h3 class="text-3xl md:text-4xl montserrat-bold tracking-wide">MISSION</h3>
                        </div>
                        <div>
                            <h4 class="text-3xl font-bold text-white mb-3">技術と表現の融合により、宇宙産業の発展に貢献。</h4>
                            <p class="text-gray-300 leading-loose">
                                新しい視点とアプローチで、宇宙ビジネスの<br>
                                社会実装を加速させます。
                                誰もが宇宙の可能性に触れられる世界を創造します。
                            </p>
                        </div>
                    </div>

                    <!-- 代表メッセージ -->
                    <div id="ceo-message" class="scroll-mt-32">
                        <div class="border-l-4 border-gray-300 pl-4 mb-12">
                            <p class="text-sm text-gray-300 tracking-widest mb-1">03 — 代表メッセージ</p>
                            <h3 class="text-3xl md:text-4xl montserrat-bold tracking-wide">MESSAGE</h3>
                        </div>

                        <div class="md:flex md:gap-12 items-center">
                            <div class="md:w-2/3 mb-8 md:mb-0 md:order-1">

                                <p class="text-2xl font-bold text-gray-300 leading-loose">
                                    見上げよう空を、つながろう宇宙で。
                                </p>
                                <br>
                                <p class="text-sm text-gray-300 leading-loose mb-6">
                                    「宇宙」と聞いて、みなさんは何を思い浮かべるでしょうか。
                                    遠い未来の話？ 限られた人だけの世界？
                                </p>
                                <p class="text-sm text-gray-300 leading-loose mb-6">
                                    私たちスペースカウボーイは、そんな固定観念を打ち破り、
                                    宇宙をもっと身近で、楽しく、ワクワクする存在にしたいと考えています。
                                </p>
                                <p class="text-sm text-gray-300 leading-loose mb-6">
                                    映像、デザイン、テクノロジー。
                                    そのすべてを融合し、宇宙産業に新しい風を吹き込みます。

                                </p>
                                <p class="text-sm text-gray-300 leading-loose mb-12">
                                    私たちの挑戦は、まだ始まったばかりです。
                                    人々が空を見上げたとき、そこに“あなたの宇宙”が感じられるように。
                                    スペースカウボーイは、誰もが参加できる宇宙時代の入口をつくっていきます。
                                </p>


                                <p class="text-base text-gray-300 leading-loose mb-6">
                                    代表取締役 CEO 香月 浩一
                                </p>
                            </div>

                            <div class="md:w-1/3 md:order-2">
                                <!-- デスクトップ用：大きい写真 -->
                                <img src="static/img/ceo_profile.jpg" alt="代表取締役 香月 浩一"
                                    class="hidden md:block w-full opacity-0 transition-opacity duration-1000 mb-4"
                                    onload="this.classList.remove('opacity-0'); this.classList.add('opacity-100');" />

                                <!-- スマホ用：写真と経歴を横並び -->
                                <div class="flex gap-4 md:block">
                                    <!-- スマホ用の小さい写真 -->
                                    <img src="static/img/ceo_profile.jpg" alt="代表取締役 香月 浩一"
                                        class="w-1/3 h-aute opacity-0 transition-opacity duration-1000 md:hidden flex-shrink-0 object-cover"
                                        onload="this.classList.remove('opacity-0'); this.classList.add('opacity-100');" />

                                    <!-- 経歴テキスト -->
                                    <p class="text-xs text-gray-400 leading-relaxed flex-1">
                                        1980年福岡出身。サンライズでガンダムなどのアニメ制作に携わった後、
                                        映像ディレクターとしてTV、CM、自治体や企業のVPを制作し、文化庁メディア芸術祭で新人賞を受賞。
                                        福岡放送での宣伝・SNS運用やウェブアプリ開発を経て、2024年に独立。
                                        応用情報技術者(経済産業省認定)。上級ウェブ解析士(2020)。慶應義塾大卒。
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 pt-6 border-t border-gray-700/50">

                    </div>

                    <!-- 会社概要 -->
                    <div id="company-info" class="scroll-mt-32">
                        <div class="border-l-4 border-gray-300 pl-4 mb-12">
                            <p class="text-sm text-gray-300 tracking-widest mb-1">04 — 会社概要</p>
                            <h3 class="text-3xl md:text-4xl montserrat-bold tracking-wide">COMPANY</h3>
                        </div>

                        <div class="text-sm text-white">
                            <table class="w-full border-separate border-spacing-x-4 border-spacing-y-0">
                                <tbody>
                                    <tr>
                                        <th
                                            class="text-left font-normal pl-4 pr-0 py-3 w-1/4 text-gray-400 border-b-2 border-gray-500">
                                            会社名</th>
                                        <td class="pl-0 pr-4 py-3 text-white border-b border-gray-700">株式会社スペースカウボーイ
                                        </td>
                                    </tr>
                                    <tr>
                                        <th
                                            class="text-left font-normal pl-4 pr-0 py-3 text-gray-400 border-b-2 border-gray-500">
                                            設立</th>
                                        <td class="pl-0 pr-4 py-3 text-white border-b border-gray-700">2025年1月</td>
                                    </tr>
                                    <tr>
                                        <th
                                            class="text-left font-normal pl-4 pr-0 py-3 text-gray-400 border-b-2 border-gray-500">
                                            代表取締役</th>
                                        <td class="pl-0 pr-4 py-3 text-white border-b border-gray-700">香月 浩一</td>
                                    </tr>
                                    <tr>
                                        <th
                                            class="text-left font-normal pl-4 pr-0 py-3 text-gray-400 border-b-2 border-gray-500">
                                            資本金</th>
                                        <td class="pl-0 pr-4 py-3 text-white border-b border-gray-700">1,000,000円</td>
                                    </tr>
                                    <tr>
                                        <th
                                            class="text-left font-normal pl-4 pr-0 py-3 text-gray-400 border-b-2 border-gray-500 align-top">
                                            所在地</th>
                                        <td class="pl-0 pr-4 py-3 text-white border-b border-gray-700">
                                            〒150-0001
                                            福岡市中央区天神4-6-28 天神ファーストビル7F
                                        </td>
                                    </tr>
                                    <tr>
                                        <th
                                            class="text-left font-normal pl-4 pr-0 py-3 text-gray-400 border-b-2 border-gray-500 align-top">
                                            事業内容</th>
                                        <td class="pl-0 pr-4 py-3 text-white border-b border-gray-700">
                                            ・宇宙関連コンテンツの企画・制作<br>
                                            ・映像・Web・アプリケーション制作<br>
                                            ・クリエイティブトータル支援
                                        </td>
                                    </tr>
                                    <tr>
                                        <th class="text-left font-normal pl-4 pr-0 py-3 text-gray-400 align-top">アクセス
                                        </th>
                                        <td class="pl-0 pr-4 py-3 text-white">
                                            地下鉄天神駅より徒歩5分<br>
                                            西鉄福岡（天神）駅より徒歩7分
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>


                    </div>


                </div>
            </div>

            <!-- CTA -->
            <div class="text-center mt-24">
                <a href="contact.html" class="hero-cta-button inline-flex items-center">
                    <span class="text-2xl mr-2">🚀</span>
                    まずはご相談ください
                </a>
            </div>

        </section>
    </div>

    <!-- Footer -->
    <footer class="text-white text-sm mt-20">
        <div class="max-w-6xl mx-auto px-8 grid grid-cols-1 md:grid-cols-4 gap-8">

            <!-- 左：会社名・住所 -->
            <div>
                <h2 class="text-xl montserrat-bold mb-2">SPACE COWBOY</h2>
                <p class="text-xs mb-1">株式会社スペースカウボーイ</p>
                <p class="text-xs text-gray-400 mb-1">〒810-0001</p>
                <p class="text-xs text-gray-400 mb-1">福岡市中央区天神4-6-28 天神ファーストビル7F</p>
            </div>

            <!-- 中央左：ナビゲーション -->
            <div>
                <ul class="space-y-1">
                    <li><a href="company.html" class="montserrat-bold hover:underline">COMPANY</a></li>
                    <li><a href="service.html" class="montserrat-bold hover:underline">SERVICE</a>
                        <ul class="ml-4 mt-1 space-y-1">
                            <li><a href="service.html#space-division"
                                    class="montserrat-regular text-sm text-gray-400 hover:underline">—
                                    SPACE
                                    DIVISION</a></li>
                            <li><a href="service.html#creative-division"
                                    class="montserrat-regular text-sm text-gray-400 hover:underline">— CREATIVE
                                    DIVISION</a></li>
                        </ul>
                    </li>
                    <li><a href="contact.html" class="montserrat-bold hover:underline">CONTACT</a></li>
                </ul>
            </div>

            <!-- 空のカラム -->
            <div></div>

            <!-- 右：SNS -->
            <div class="text-right">
                <ul class="space-y-2">
                    <li class="flex items-center gap-2 justify-end">
                        <!-- Xアイコン -->
                        <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                            <path
                                d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z" />
                        </svg>
                        <a href="https://x.com/cutkey5" class="montserrat-regular hover:underline"
                            target="_blank">X(twitter)</a>
                    </li>
                </ul>
            </div>

        </div>

        <!-- 下部：コピーライト -->
        <div class="max-w-6xl mx-auto px-6 text-xs text-gray-500 py-12">
            <div class="border-t border-gray-600 pt-8">
                <div
                    class="max-w-6xl mx-auto flex flex-col md:flex-row justify-between items-start md:items-center gap-2">
                    <div class="flex flex-col md:flex-row gap-4">
                        <a href="privacy.html" class="hover:underline">プライバシーポリシー</a>
                        <a href="terms.html" class="hover:underline">利用規約</a>
                        <a href="tokushoho.html" class="hover:underline">特定商取引法に基づく表記</a>
                    </div>
                    <div>
                        &copy;2025 SPACE COWBOY .Inc
                    </div>
                </div>
            </div>
        </div>

    </footer>

    <script src="static/js/main.js"></script>
    <script>
        // about.html専用の初期化
        SpaceCowboy.init({
            starCount: 100,
            enableSmoothScroll: false
        });
    </script>
</body>

</html>